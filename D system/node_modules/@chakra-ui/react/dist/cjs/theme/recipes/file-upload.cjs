"use strict";
'use strict';

var anatomy = require('../../anatomy.cjs');
var config = require('../../styled-system/config.cjs');

const fileUploadSlotRecipe = config.defineSlotRecipe({
  className: "chakra-file-upload",
  slots: anatomy.fileUploadAnatomy.keys(),
  base: {
    root: {
      display: "flex",
      flexDirection: "column",
      gap: "4",
      width: "100%",
      alignItems: "flex-start"
    },
    label: {
      fontWeight: "medium",
      textStyle: "sm"
    },
    dropzone: {
      background: "bg",
      borderRadius: "l3",
      borderWidth: "2px",
      borderStyle: "dashed",
      display: "flex",
      alignItems: "center",
      flexDirection: "column",
      gap: "4",
      justifyContent: "center",
      minHeight: "2xs",
      px: "3",
      py: "2",
      transition: "backgrounds",
      focusVisibleRing: "outside",
      _hover: {
        bg: "bg.subtle"
      },
      _dragging: {
        bg: "colorPalette.subtle",
        borderStyle: "solid",
        borderColor: "colorPalette.solid"
      }
    },
    dropzoneContent: {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      textAlign: "center",
      gap: "1",
      textStyle: "sm"
    },
    item: {
      pos: "relative",
      textStyle: "sm",
      animationName: "fade-in",
      animationDuration: "moderate",
      background: "bg",
      borderRadius: "l2",
      borderWidth: "1px",
      width: "100%",
      display: "flex",
      alignItems: "center",
      gap: "3",
      p: "4"
    },
    itemGroup: {
      width: "100%",
      display: "flex",
      flexDirection: "column",
      gap: "3",
      _empty: {
        display: "none"
      }
    },
    itemName: {
      color: "fg",
      fontWeight: "medium",
      lineClamp: "1"
    },
    itemContent: {
      display: "flex",
      flexDirection: "column",
      gap: "0.5",
      flex: "1"
    },
    itemSizeText: {
      color: "fg.muted",
      textStyle: "xs"
    },
    itemDeleteTrigger: {
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      alignSelf: "flex-start",
      boxSize: "5",
      p: "2px",
      color: "fg.muted",
      cursor: "button"
    },
    itemPreview: {
      color: "fg.muted",
      _icon: {
        boxSize: "4.5"
      }
    }
  },
  defaultVariants: {}
});

exports.fileUploadSlotRecipe = fileUploadSlotRecipe;
