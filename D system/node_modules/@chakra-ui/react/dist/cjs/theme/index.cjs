"use strict";
'use strict';

var config = require('../styled-system/config.cjs');
var breakpoints = require('./breakpoints.cjs');
var globalCss = require('./global-css.cjs');
var layerStyles = require('./layer-styles.cjs');
var motionStyles = require('./motion-styles.cjs');
var recipes = require('./recipes.cjs');
var colors = require('./semantic-tokens/colors.cjs');
var radii = require('./semantic-tokens/radii.cjs');
var shadows = require('./semantic-tokens/shadows.cjs');
var slotRecipes = require('./slot-recipes.cjs');
var textStyles = require('./text-styles.cjs');
var animations = require('./tokens/animations.cjs');
var aspectRatios = require('./tokens/aspect-ratios.cjs');
var blurs = require('./tokens/blurs.cjs');
var borders = require('./tokens/borders.cjs');
var colors$1 = require('./tokens/colors.cjs');
var cursor = require('./tokens/cursor.cjs');
var durations = require('./tokens/durations.cjs');
var easings = require('./tokens/easings.cjs');
var fontSizes = require('./tokens/font-sizes.cjs');
var fontWeights = require('./tokens/font-weights.cjs');
var fonts = require('./tokens/fonts.cjs');
var keyframes = require('./tokens/keyframes.cjs');
var letterSpacing = require('./tokens/letter-spacing.cjs');
var lineHeights = require('./tokens/line-heights.cjs');
var radius = require('./tokens/radius.cjs');
var sizes = require('./tokens/sizes.cjs');
var spacing = require('./tokens/spacing.cjs');
var zIndices = require('./tokens/z-indices.cjs');

const defaultThemeConfig = config.defineConfig({
  preflight: true,
  cssVarsPrefix: "chakra",
  cssVarsRoot: ":where(html, .chakra-theme)",
  globalCss: globalCss.globalCss,
  theme: {
    breakpoints: breakpoints.breakpoints,
    keyframes: keyframes.keyframes,
    tokens: {
      aspectRatios: aspectRatios.aspectRatios,
      animations: animations.animations,
      blurs: blurs.blurs,
      borders: borders.borders,
      colors: colors$1.colors,
      durations: durations.durations,
      easings: easings.easings,
      fonts: fonts.fonts,
      fontSizes: fontSizes.fontSizes,
      fontWeights: fontWeights.fontWeights,
      letterSpacings: letterSpacing.letterSpacings,
      lineHeights: lineHeights.lineHeights,
      radii: radius.radii,
      spacing: spacing.spacing,
      sizes: sizes.sizes,
      zIndex: zIndices.zIndices,
      cursor: cursor.cursor
    },
    semanticTokens: {
      colors: colors.semanticColors,
      shadows: shadows.semanticShadows,
      radii: radii.semanticRadii
    },
    recipes: recipes.recipes,
    slotRecipes: slotRecipes.slotRecipes,
    textStyles: textStyles.textStyles,
    layerStyles: layerStyles.layerStyles,
    animationStyles: motionStyles.animationStyles
  }
});

exports.defaultThemeConfig = defaultThemeConfig;
