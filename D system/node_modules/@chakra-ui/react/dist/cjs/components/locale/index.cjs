"use strict";
'use strict';

var locale = require('@ark-ui/react/locale');



Object.defineProperty(exports, "LocaleProvider", {
  enumerable: true,
  get: function () { return locale.LocaleProvider; }
});
Object.defineProperty(exports, "useFilter", {
  enumerable: true,
  get: function () { return locale.useFilter; }
});
Object.defineProperty(exports, "useLocaleContext", {
  enumerable: true,
  get: function () { return locale.useLocaleContext; }
});
