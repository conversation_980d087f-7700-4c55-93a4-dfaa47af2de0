"use strict";
'use strict';

var table = require('./table.cjs');
var namespace = require('./namespace.cjs');



exports.TableBody = table.TableBody;
exports.TableCaption = table.TableCaption;
exports.TableCell = table.TableCell;
exports.TableColumn = table.TableColumn;
exports.TableColumnGroup = table.TableColumnGroup;
exports.TableColumnHeader = table.TableColumnHeader;
exports.TableFooter = table.TableFooter;
exports.TableHeader = table.TableHeader;
exports.TableRoot = table.TableRoot;
exports.TableRootPropsProvider = table.TableRootPropsProvider;
exports.TableRow = table.TableRow;
exports.TableScrollArea = table.TableScrollArea;
exports.useTableStyles = table.useTableStyles;
exports.Table = namespace;
