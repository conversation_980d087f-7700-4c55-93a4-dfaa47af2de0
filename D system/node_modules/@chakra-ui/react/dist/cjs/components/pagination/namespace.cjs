"use strict";
'use strict';

var pagination = require('./pagination.cjs');



exports.Context = pagination.PaginationContext;
exports.Ellipsis = pagination.PaginationEllipsis;
exports.Item = pagination.PaginationItem;
exports.Items = pagination.PaginationItems;
exports.NextTrigger = pagination.PaginationNextTrigger;
exports.PageText = pagination.PaginationPageText;
exports.PrevTrigger = pagination.PaginationPrevTrigger;
exports.PropsProvider = pagination.PaginationPropsProvider;
exports.Root = pagination.PaginationRoot;
exports.RootProvider = pagination.PaginationRootProvider;
