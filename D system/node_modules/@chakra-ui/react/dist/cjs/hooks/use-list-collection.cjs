"use strict";
"use client";
'use strict';

var React = require('react');
var useCallbackRef = require('./use-callback-ref.cjs');
var collection = require('@ark-ui/react/collection');

function useListCollection(props) {
  const { initialItems = [], filter, limit, ...collectionOptions } = props;
  const create = (items) => {
    return collection.createListCollection({ ...collectionOptions, items });
  };
  const [collection$1, setCollectionImpl] = React.useState(
    () => create(limit != null ? initialItems.slice(0, limit) : initialItems)
  );
  const setCollection = useCallbackRef.useCallbackRef((collection2) => {
    setCollectionImpl(
      limit == null ? collection2 : collection2.copy(collection2.items.slice(0, limit))
    );
  });
  return {
    collection: collection$1,
    filter: (inputValue) => {
      if (!filter) return;
      let filtered = create(initialItems).filter(
        (itemString) => filter(itemString, inputValue)
      );
      setCollection(filtered);
    },
    set: useCallbackRef.useCallbackRef((items) => {
      setCollection(create(items));
    }),
    reset: useCallbackRef.useCallbackRef(() => {
      setCollection(create(initialItems));
    }),
    clear: useCallbackRef.useCallbackRef(() => {
      setCollection(create([]));
    }),
    insert: useCallbackRef.useCallbackRef((index, ...items) => {
      setCollection(collection$1.insert(index, ...items));
    }),
    insertBefore: useCallbackRef.useCallbackRef((value, ...items) => {
      setCollection(collection$1.insertBefore(value, ...items));
    }),
    insertAfter: useCallbackRef.useCallbackRef((value, ...items) => {
      setCollection(collection$1.insertAfter(value, ...items));
    }),
    remove: useCallbackRef.useCallbackRef((...itemOrValues) => {
      setCollection(collection$1.remove(...itemOrValues));
    }),
    move: useCallbackRef.useCallbackRef((value, to) => {
      setCollection(collection$1.move(value, to));
    }),
    moveBefore: useCallbackRef.useCallbackRef((value, ...values) => {
      setCollection(collection$1.moveBefore(value, ...values));
    }),
    moveAfter: useCallbackRef.useCallbackRef((value, ...values) => {
      setCollection(collection$1.moveAfter(value, ...values));
    }),
    reorder: useCallbackRef.useCallbackRef((from, to) => {
      setCollection(collection$1.reorder(from, to));
    }),
    append: useCallbackRef.useCallbackRef((...items) => {
      setCollection(collection$1.append(...items));
    }),
    prepend: useCallbackRef.useCallbackRef((...items) => {
      setCollection(collection$1.prepend(...items));
    }),
    update: useCallbackRef.useCallbackRef((value, item) => {
      setCollection(collection$1.update(value, item));
    })
  };
}

exports.useListCollection = useListCollection;
