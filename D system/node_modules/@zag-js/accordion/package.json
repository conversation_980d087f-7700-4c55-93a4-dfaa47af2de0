{"name": "@zag-js/accordion", "version": "1.12.2", "description": "Core logic for the accordion widget implemented as a state machine", "keywords": ["js", "machine", "xstate", "statechart", "component", "chakra-ui", "accordion"], "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/chakra-ui/zag#readme", "license": "MIT", "repository": "https://github.com/chakra-ui/zag/tree/main/packages/accordion", "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "bugs": {"url": "https://github.com/chakra-ui/zag/issues"}, "dependencies": {"@zag-js/anatomy": "1.12.2", "@zag-js/core": "1.12.2", "@zag-js/dom-query": "1.12.2", "@zag-js/types": "1.12.2", "@zag-js/utils": "1.12.2"}, "devDependencies": {"clean-package": "2.2.0"}, "clean-package": "../../../clean-package.config.json", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "./package.json": "./package.json"}, "scripts": {"build": "tsup", "lint": "eslint src", "typecheck": "tsc --noEmit"}}