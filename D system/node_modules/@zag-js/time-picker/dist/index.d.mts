import * as _zag_js_anatomy from '@zag-js/anatomy';
import { RequiredBy, DirectionProperty, CommonProperties, PropTypes, NormalizeProps } from '@zag-js/types';
import { Time } from '@internationalized/date';
export { Time } from '@internationalized/date';
import * as _zag_js_core from '@zag-js/core';
import { Service, EventObject, Machine } from '@zag-js/core';
import { Placement, PositioningOptions } from '@zag-js/popper';
export { PositioningOptions } from '@zag-js/popper';

declare const anatomy: _zag_js_anatomy.AnatomyInstance<"cell" | "clearTrigger" | "column" | "content" | "control" | "input" | "label" | "positioner" | "root" | "spacer" | "trigger">;

type TimePeriod = "am" | "pm";
type TimeUnit = "hour" | "minute" | "second" | "period";
interface OpenChangeDetails {
    open: boolean;
}
interface ValueChangeDetails {
    value: Time | null;
    valueAsString: string;
}
interface FocusChangeDetails extends ValueChangeDetails {
    focusedUnit: TimeUnit;
    focusedValue: any;
}
type ElementIds = Partial<{
    trigger: string;
    input: string;
    positioner: string;
    content: string;
    clearTrigger: string;
    control: string;
    column(unit: TimeUnit): string;
}>;
interface TimePickerProps extends DirectionProperty, CommonProperties {
    /**
     * The locale (BCP 47 language tag) to use when formatting the time.
     */
    locale?: string | undefined;
    /**
     * The controlled selected time.
     */
    value?: Time | null | undefined;
    /**
     * The initial selected time when rendered.
     * Use when you don't need to control the selected time.
     */
    defaultValue?: Time | null | undefined;
    /**
     * Whether the timepicker is open
     */
    open?: boolean | undefined;
    /**
     * Whether the timepicker open state is controlled by the user
     */
    defaultOpen?: boolean | undefined;
    /**
     * The ids of the elements in the date picker. Useful for composition.
     */
    ids?: ElementIds | undefined;
    /**
     * The `name` attribute of the input element.
     */
    name?: string | undefined;
    /**
     * The user provided options used to position the time picker content
     */
    positioning?: PositioningOptions | undefined;
    /**
     * The placeholder text of the input.
     */
    placeholder?: string | undefined;
    /**
     * Whether the time picker is disabled.
     */
    disabled?: boolean | undefined;
    /**
     * Whether the time picker is read-only.
     */
    readOnly?: boolean | undefined;
    /**
     * The minimum time that can be selected.
     */
    min?: Time | undefined;
    /**
     * The maximum time that can be selected.
     */
    max?: Time | undefined;
    /**
     * The steps of each time unit.
     */
    steps?: {
        hour?: number;
        minute?: number;
        second?: number;
    } | undefined;
    /**
     * Whether to show the seconds.
     */
    allowSeconds?: boolean | undefined;
    /**
     * Function called when the value changes.
     */
    onValueChange?: ((value: ValueChangeDetails) => void) | undefined;
    /**
     * Function called when the time picker opens or closes.
     */
    onOpenChange?: ((details: OpenChangeDetails) => void) | undefined;
    /**
     * Function called when the focused date changes.
     */
    onFocusChange?: ((details: FocusChangeDetails) => void) | undefined;
    /**
     * Whether to disable the interaction outside logic
     */
    disableLayer?: boolean | undefined;
}
type PropsWithDefault = "positioning" | "locale";
interface PrivateContext {
    /**
     * The computed placement (maybe different from initial placement)
     */
    currentPlacement?: Placement | undefined;
    /**
     * Whether the calendar should restore focus to the input when it closes.
     */
    restoreFocus?: boolean | undefined;
    /**
     * The focused unit column
     */
    focusedColumn: TimeUnit;
    /**
     * The focused cell value
     */
    focusedValue: any;
    /**
     * The current time
     */
    currentTime: Time | null;
    /**
     * The selected time
     */
    value: Time | null;
}
type ComputedContext = Readonly<{
    /**
     * The selected time as a string
     */
    valueAsString: string;
    /**
     * Whether the time picker is in 12-hour format (based on the locale)
     */
    hour12: boolean;
    /**
     * The period of the time (AM/PM)
     */
    period: TimePeriod | null;
}>;
interface TimePickerSchema {
    state: "idle" | "focused" | "open";
    context: PrivateContext;
    props: RequiredBy<TimePickerProps, PropsWithDefault>;
    computed: ComputedContext;
    private: PrivateContext;
    action: string;
    event: EventObject;
    effect: string;
    guard: string;
}
type TimePickerService = Service<TimePickerSchema>;
type TimePickerMachine = Machine<TimePickerSchema>;
interface ColumnProps {
    unit: TimeUnit;
}
interface CellProps {
    value: number;
}
interface PeriodCellProps {
    value: TimePeriod;
}
interface Cell {
    label: string;
    value: number;
}
interface TimePickerApi<T extends PropTypes = PropTypes> {
    /**
     * Whether the input is focused
     */
    focused: boolean;
    /**
     * Whether the time picker is open
     */
    open: boolean;
    /**
     * The selected time
     */
    value: Time | null;
    /**
     * The selected time as a string
     */
    valueAsString: string | undefined;
    /**
     * Whether the time picker is in 12-hour format (based on the locale prop)
     */
    hour12: boolean;
    /**
     * Function to reposition the time picker content
     */
    reposition(options?: PositioningOptions): void;
    /**
     * Function to open the time picker
     */
    setOpen(nextOpen: boolean): void;
    /**
     * Function to clear the selected time
     */
    clearValue(): void;
    /**
     * Function to set the selected time
     */
    setValue(value: string | Time): void;
    /**
     * Function to set the focused time unit
     */
    setUnitValue(unit: "period", value: TimePeriod): void;
    /**
     * Function to set the focused time unit
     */
    setUnitValue(unit: TimeUnit, value: number): void;
    /**
     * Get the available hours that will be displayed in the time picker
     */
    getHours(): Cell[];
    /**
     * Get the available minutes that will be displayed in the time picker
     */
    getMinutes(): Cell[];
    /**
     * Get the available seconds that will be displayed in the time picker
     */
    getSeconds(): Cell[];
    getRootProps(): T["element"];
    getLabelProps(): T["element"];
    getControlProps(): T["element"];
    getInputProps(): T["element"];
    getTriggerProps(): T["element"];
    getSpacerProps(): T["element"];
    getClearTriggerProps(): T["element"];
    getPositionerProps(): T["element"];
    getContentProps(): T["element"];
    getColumnProps(options: ColumnProps): T["element"];
    getHourCellProps(options: CellProps): T["element"];
    getMinuteCellProps(options: CellProps): T["element"];
    getSecondCellProps(options: CellProps): T["element"];
    getPeriodCellProps(options: PeriodCellProps): T["element"];
}

declare function connect<T extends PropTypes>(service: TimePickerService, normalize: NormalizeProps<T>): TimePickerApi<T>;

declare const machine: _zag_js_core.Machine<TimePickerSchema>;

interface TimeSegment {
    hour: number;
    minute: number;
    second: number;
    millisecond: number;
}
declare function parse(value: Partial<TimeSegment>): Time;

declare const props: (keyof TimePickerProps)[];
declare const splitProps: <Props extends TimePickerProps>(props: Props) => [TimePickerProps, Omit<Props, keyof TimePickerProps>];

export { type TimePickerApi as Api, type CellProps, type ColumnProps, type ElementIds, type FocusChangeDetails, type TimePickerMachine as Machine, type OpenChangeDetails, type PeriodCellProps, type TimePickerProps as Props, type TimePickerService as Service, type TimePeriod, type TimeUnit, type ValueChangeDetails, anatomy, connect, machine, parse, props, splitProps };
