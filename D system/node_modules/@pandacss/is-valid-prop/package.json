{"name": "@pandacss/is-valid-prop", "version": "0.53.6", "description": "Common error messages for css panda", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "author": "<PERSON><PERSON> <<EMAIL>>", "sideEffects": false, "homepage": "https://panda-css.com", "repository": {"type": "git", "url": "git+https://github.com/chakra-ui/panda.git", "directory": "packages/is-valid-prop"}, "publishConfig": {"access": "public"}, "exports": {".": {"source": "./src/index.ts", "types": "./dist/index.d.ts", "require": "./dist/index.js", "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "./package.json": "./package.json"}, "files": ["dist"], "devDependencies": {"mdn-data": "^2.15.0"}, "scripts": {"mdn": "tsx scripts/prepare.ts", "build": "tsup src/index.ts --format=esm,cjs --dts && tsx scripts/postbuild.ts", "build-fast": "tsup src/index.ts --format=esm,cjs --no-dts", "dev": "tsup src/index.ts --watch --format=esm,cjs --no-dts --onSuccess=\"tsx scripts/postbuild.ts\""}}