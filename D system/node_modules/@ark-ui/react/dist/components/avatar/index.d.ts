export type { StatusChangeDetails as AvatarStatusChangeDetails } from '@zag-js/avatar';
export { AvatarContext, type AvatarContextProps } from './avatar-context';
export { AvatarFallback, type AvatarFallbackBaseProps, type AvatarFallbackProps } from './avatar-fallback';
export { AvatarImage, type AvatarImageBaseProps, type AvatarImageProps } from './avatar-image';
export { AvatarRoot, type AvatarRootBaseProps, type AvatarRootProps } from './avatar-root';
export { AvatarRootProvider, type AvatarRootProviderBaseProps, type AvatarRootProviderProps, } from './avatar-root-provider';
export { avatarAnatomy } from './avatar.anatomy';
export { useAvatar, type UseAvatarProps, type UseAvatarReturn } from './use-avatar';
export { useAvatarContext, type UseAvatarContext } from './use-avatar-context';
export * as Avatar from './avatar';
