#!/usr/bin/env node

/**
 * Figma Sync Script
 *
 * This script demonstrates how to sync components and design tokens
 * from the Figma design system file to the RSC library.
 *
 * Usage:
 *   node scripts/sync-figma.js
 *
 * Environment Variables:
 *   FIGMA_ACCESS_TOKEN - Your Figma personal access token
 *   FIGMA_FILE_ID - The Figma file ID to sync from
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

const FIGMA_ACCESS_TOKEN = process.env.FIGMA_ACCESS_TOKEN;
const FIGMA_FILE_ID = process.env.FIGMA_FILE_ID || 'CYlewnUysOEtOe6BVlSlZ3';
const FIGMA_API_BASE = 'https://api.figma.com/v1';

/**
 * Fetch data from Figma API with query parameters
 */
async function fetchFigmaData(endpoint, queryParams = {}) {
  if (!FIGMA_ACCESS_TOKEN) {
    console.log('⚠️  No Figma access token found. Using mock data.');
    return null;
  }

  try {
    const url = new URL(`${FIGMA_API_BASE}${endpoint}`);
    Object.entries(queryParams).forEach(([key, value]) => {
      url.searchParams.append(key, value);
    });

    console.log(`📡 Fetching: ${url.pathname}${url.search}`);

    const response = await fetch(url.toString(), {
      headers: {
        'X-Figma-Token': FIGMA_ACCESS_TOKEN,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Figma API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('❌ Error fetching from Figma:', error.message);
    return null;
  }
}

/**
 * Extract design tokens from Figma file
 */
async function extractDesignTokens() {
  console.log('🎨 Extracting design tokens from Figma...');

  // First, get just the file metadata to check for styles
  const fileData = await fetchFigmaData(`/files/${FIGMA_FILE_ID}`, {
    depth: '1',
    geometry: 'paths'
  });

  if (!fileData) {
    console.log('⚠️  Using mock design tokens');
    return generateMockTokens();
  }

  const tokens = [];

  // Extract tokens from styles
  if (fileData.styles) {
    console.log(`📋 Found ${Object.keys(fileData.styles).length} styles in Figma`);

    Object.entries(fileData.styles).forEach(([styleId, style]) => {
      const token = {
        name: style.name.toLowerCase().replace(/\s+/g, '-').replace(/\//g, '-'),
        value: extractStyleValue(style),
        type: getTokenType(style.styleType),
        description: style.description || `${style.name} from Figma`,
        figmaId: styleId,
      };
      tokens.push(token);
    });
  } else {
    console.log('⚠️  No styles found in Figma file, using mock tokens');
    return generateMockTokens();
  }

  console.log(`✅ Extracted ${tokens.length} design tokens`);
  return tokens;
}

/**
 * Extract components from Figma file
 */
async function extractComponents() {
  console.log('🧩 Extracting components from Figma...');

  // Get file with limited depth to avoid large response
  const fileData = await fetchFigmaData(`/files/${FIGMA_FILE_ID}`, {
    depth: '2'
  });

  if (!fileData) {
    console.log('⚠️  Using mock components');
    return generateMockComponents();
  }

  const components = [];

  // Check for components in the file
  if (fileData.components) {
    console.log(`🧩 Found ${Object.keys(fileData.components).length} components in Figma`);

    Object.entries(fileData.components).forEach(([componentId, component]) => {
      const rscComponent = {
        id: component.name.toLowerCase().replace(/\s+/g, '-'),
        name: component.name,
        description: component.description || `${component.name} component from Figma`,
        figmaId: componentId,
        figmaUrl: `https://www.figma.com/design/${FIGMA_FILE_ID}?node-id=${componentId}`,
        category: categorizeComponent(component.name),
        variants: [{ name: 'Default', props: {} }], // Simplified for now
      };
      components.push(rscComponent);
    });
  }

  // Also parse document structure for frames
  if (fileData.document && fileData.document.children) {
    console.log(`📑 Found ${fileData.document.children.length} pages in Figma`);

    for (const page of fileData.document.children) {
      console.log(`  📄 Page: ${page.name}`);
      if (page.name.toLowerCase().includes('components') ||
          page.name.toLowerCase().includes('design system') ||
          page.name.toLowerCase().includes('library')) {
        const pageComponents = parsePageComponents(page);
        components.push(...pageComponents);
      }
    }
  }

  console.log(`✅ Extracted ${components.length} components`);
  return components;
}

/**
 * Parse components from a Figma page
 */
function parsePageComponents(page) {
  const components = [];

  if (page.children) {
    page.children.forEach(frame => {
      if (frame.type === 'FRAME' || frame.type === 'COMPONENT_SET') {
        const component = {
          id: frame.name.toLowerCase().replace(/\s+/g, '-'),
          name: frame.name,
          description: frame.description || `${frame.name} component from Figma`,
          figmaId: frame.id,
          figmaUrl: `https://www.figma.com/design/${FIGMA_FILE_ID}?node-id=${frame.id}`,
          category: categorizeComponent(frame.name),
          variants: extractVariants(frame),
        };
        components.push(component);
      }
    });
  }

  return components;
}

/**
 * Extract variants from a Figma component
 */
function extractVariants(frame) {
  const variants = [];

  if (frame.children) {
    frame.children.forEach(child => {
      variants.push({
        name: child.name,
        props: extractPropsFromVariant(child),
        figmaId: child.id,
      });
    });
  }

  return variants.length > 0 ? variants : [{ name: 'Default', props: {} }];
}

/**
 * Extract props from variant name
 */
function extractPropsFromVariant(variant) {
  const props = {};
  const name = variant.name.toLowerCase();

  // Extract variant type
  if (name.includes('primary')) props.variant = 'primary';
  else if (name.includes('secondary')) props.variant = 'secondary';
  else if (name.includes('outline')) props.variant = 'outline';

  // Extract size
  if (name.includes('large')) props.size = 'lg';
  else if (name.includes('small')) props.size = 'sm';
  else props.size = 'md';

  return props;
}

/**
 * Categorize component based on name
 */
function categorizeComponent(name) {
  const lowerName = name.toLowerCase();

  if (lowerName.includes('button') || lowerName.includes('input') || lowerName.includes('card')) {
    return 'shared-components';
  } else if (lowerName.includes('color') || lowerName.includes('typography')) {
    return 'foundations';
  } else if (lowerName.includes('icon') || lowerName.includes('illustration')) {
    return 'shared-assets';
  }

  return 'shared-components';
}

/**
 * Generate CSS from design tokens
 */
function generateCSS(tokens) {
  const cssVariables = tokens.map(token => `  --${token.name}: ${token.value};`).join('\n');
  return `:root {\n${cssVariables}\n}`;
}

/**
 * Generate mock data when Figma API is not available
 */
function generateMockTokens() {
  return [
    { name: 'primary-500', value: '#3b82f6', type: 'color', description: 'Primary color' },
    { name: 'gray-100', value: '#f3f4f6', type: 'color', description: 'Light gray' },
    { name: 'font-size-base', value: '16px', type: 'typography', description: 'Base font size' },
    { name: 'space-4', value: '16px', type: 'spacing', description: 'Base spacing' },
  ];
}

function generateMockComponents() {
  return [
    {
      id: 'button',
      name: 'Button',
      description: 'Interactive button component',
      category: 'shared-components',
      variants: [
        { name: 'Primary', props: { variant: 'primary' } },
        { name: 'Secondary', props: { variant: 'secondary' } },
      ],
    },
  ];
}

/**
 * Utility functions
 */
function extractStyleValue(style) {
  // This would extract actual values from Figma styles
  // For now, return placeholder values
  switch (style.styleType) {
    case 'FILL': return '#000000';
    case 'TEXT': return '16px';
    case 'EFFECT': return '0 2px 4px rgba(0,0,0,0.1)';
    default: return 'auto';
  }
}

function getTokenType(styleType) {
  switch (styleType) {
    case 'FILL': return 'color';
    case 'TEXT': return 'typography';
    case 'EFFECT': return 'shadow';
    default: return 'other';
  }
}

/**
 * Main sync function
 */
async function syncFigma() {
  console.log('🚀 Starting Figma sync...');
  console.log(`📁 File ID: ${FIGMA_FILE_ID}`);

  try {
    // Extract design tokens
    const tokens = await extractDesignTokens();

    // Extract components
    const components = await extractComponents();

    // Generate CSS from tokens
    const css = generateCSS(tokens);

    // Write results to files
    const outputDir = path.join(__dirname, '..', 'figma-output');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    fs.writeFileSync(
      path.join(outputDir, 'design-tokens.json'),
      JSON.stringify(tokens, null, 2)
    );

    fs.writeFileSync(
      path.join(outputDir, 'components.json'),
      JSON.stringify(components, null, 2)
    );

    fs.writeFileSync(
      path.join(outputDir, 'tokens.css'),
      css
    );

    console.log('✅ Sync completed successfully!');
    console.log(`📄 Design tokens: ${tokens.length}`);
    console.log(`🧩 Components: ${components.length}`);
    console.log(`📁 Output directory: ${outputDir}`);

  } catch (error) {
    console.error('❌ Sync failed:', error.message);
    process.exit(1);
  }
}

// Run the sync if this script is executed directly
if (require.main === module) {
  syncFigma();
}

module.exports = { syncFigma, extractDesignTokens, extractComponents };
