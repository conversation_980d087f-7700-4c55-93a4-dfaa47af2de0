#!/usr/bin/env node

/**
 * Generate all 94 documentation pages from Figma structure
 */

const fs = require('fs');
const path = require('path');

// All 94 pages from Figma organized in the same order
const docsPages = [
  // Getting Started
  { title: 'Quick Start', href: '/docs/quick-start', category: 'Getting Started' },

  // Thumbnail
  { title: 'Overview', href: '/docs/thumbnail/overview', category: 'Thumbnail' },

  // Foundations
  { title: 'Colors ✨ NEW ✨', href: '/docs/foundations/colors', category: 'Foundations' },
  { title: 'Typography', href: '/docs/foundations/typography', category: 'Foundations' },
  { title: 'Logos', href: '/docs/foundations/logos', category: 'Foundations' },
  { title: 'Icons', href: '/docs/foundations/icons', category: 'Foundations' },
  { title: 'Misc icons ✨ NEW ✨', href: '/docs/foundations/misc-icons', category: 'Foundations' },
  { title: 'Shadows & blurs', href: '/docs/foundations/shadows-blurs', category: 'Foundations' },
  { title: 'Grids & spacing', href: '/docs/foundations/grids-spacing', category: 'Foundations' },
  { title: 'Portfolio mockups', href: '/docs/foundations/portfolio-mockups', category: 'Foundations' },
  { title: 'Design annotations', href: '/docs/foundations/design-annotations', category: 'Foundations' },

  // Shared Components
  { title: 'Buttons', href: '/docs/shared-components/buttons', category: 'Shared Components' },
  { title: 'Button groups', href: '/docs/shared-components/button-groups', category: 'Shared Components' },
  { title: 'Badges ✨ NEW ✨', href: '/docs/shared-components/badges', category: 'Shared Components' },
  { title: 'Tags', href: '/docs/shared-components/tags', category: 'Shared Components' },
  { title: 'Dropdowns', href: '/docs/shared-components/dropdowns', category: 'Shared Components' },
  { title: 'Inputs', href: '/docs/shared-components/inputs', category: 'Shared Components' },
  { title: 'Toggles', href: '/docs/shared-components/toggles', category: 'Shared Components' },
  { title: 'Checkboxes', href: '/docs/shared-components/checkboxes', category: 'Shared Components' },
  { title: 'Checkbox groups', href: '/docs/shared-components/checkbox-groups', category: 'Shared Components' },
  { title: 'Avatars ✨ NEW ✨', href: '/docs/shared-components/avatars', category: 'Shared Components' },
  { title: 'Tooltips', href: '/docs/shared-components/tooltips', category: 'Shared Components' },
  { title: 'Progress indicators', href: '/docs/shared-components/progress-indicators', category: 'Shared Components' },
  { title: 'Sliders', href: '/docs/shared-components/sliders', category: 'Shared Components' },

  // Shared Assets
  { title: 'Log in and sign up pages ✨ NEW ✨', href: '/docs/shared-assets/login-signup-pages', category: 'Shared Assets' },
  { title: '404 pages ✨ NEW ✨', href: '/docs/shared-assets/404-pages', category: 'Shared Assets' },
  { title: 'Email templates', href: '/docs/shared-assets/email-templates', category: 'Shared Assets' },
  { title: 'Miscellaneous assets', href: '/docs/shared-assets/miscellaneous-assets', category: 'Shared Assets' },
  { title: 'Background elements ✨ NEW ✨', href: '/docs/shared-assets/background-elements', category: 'Shared Assets' },

  // Marketing Website Examples
  { title: 'Landing pages', href: '/docs/marketing-examples/landing-pages', category: 'Marketing Website Examples' },
  { title: 'Pricing pages', href: '/docs/marketing-examples/pricing-pages', category: 'Marketing Website Examples' },
  { title: 'Blogs', href: '/docs/marketing-examples/blogs', category: 'Marketing Website Examples' },
  { title: 'Blog posts', href: '/docs/marketing-examples/blog-posts', category: 'Marketing Website Examples' },
  { title: 'About pages', href: '/docs/marketing-examples/about-pages', category: 'Marketing Website Examples' },
  { title: 'Contact pages', href: '/docs/marketing-examples/contact-pages', category: 'Marketing Website Examples' },
  { title: 'Team pages', href: '/docs/marketing-examples/team-pages', category: 'Marketing Website Examples' },
  { title: 'Legal pages', href: '/docs/marketing-examples/legal-pages', category: 'Marketing Website Examples' },
  { title: 'FAQ pages', href: '/docs/marketing-examples/faq-pages', category: 'Marketing Website Examples' },
  { title: 'Log in and sign up pages', href: '/docs/marketing-examples/login-signup', category: 'Marketing Website Examples' },
  { title: '404 pages', href: '/docs/marketing-examples/404-pages', category: 'Marketing Website Examples' },

  // Marketing Website Components
  { title: 'Header navigation', href: '/docs/marketing-components/header-navigation', category: 'Marketing Website Components' },
  { title: 'Header sections', href: '/docs/marketing-components/header-sections', category: 'Marketing Website Components' },
  { title: 'Features sections', href: '/docs/marketing-components/features-sections', category: 'Marketing Website Components' },
  { title: 'Pricing sections', href: '/docs/marketing-components/pricing-sections', category: 'Marketing Website Components' },
  { title: 'CTA sections', href: '/docs/marketing-components/cta-sections', category: 'Marketing Website Components' },
  { title: 'Metrics sections', href: '/docs/marketing-components/metrics-sections', category: 'Marketing Website Components' },
  { title: 'Newsletter CTA sections', href: '/docs/marketing-components/newsletter-cta-sections', category: 'Marketing Website Components' },
  { title: 'Testimonial sections', href: '/docs/marketing-components/testimonial-sections', category: 'Marketing Website Components' },
  { title: 'Social proof sections', href: '/docs/marketing-components/social-proof-sections', category: 'Marketing Website Components' },
  { title: 'Blog sections ✨ NEW ✨', href: '/docs/marketing-components/blog-sections', category: 'Marketing Website Components' },
  { title: 'Content', href: '/docs/marketing-components/content', category: 'Marketing Website Components' },
  { title: 'Contact sections', href: '/docs/marketing-components/contact-sections', category: 'Marketing Website Components' },
  { title: 'Team sections', href: '/docs/marketing-components/team-sections', category: 'Marketing Website Components' },
  { title: 'Careers sections', href: '/docs/marketing-components/careers-sections', category: 'Marketing Website Components' },
  { title: 'FAQ sections', href: '/docs/marketing-components/faq-sections', category: 'Marketing Website Components' },
  { title: 'Footers', href: '/docs/marketing-components/footers', category: 'Marketing Website Components' },
  { title: 'Banners', href: '/docs/marketing-components/banners', category: 'Marketing Website Components' },

  // Application Examples
  { title: 'Dashboards', href: '/docs/application-examples/dashboards', category: 'Application Examples' },
  { title: 'Settings pages', href: '/docs/application-examples/settings-pages', category: 'Application Examples' },
  { title: 'Informational pages', href: '/docs/application-examples/informational-pages', category: 'Application Examples' },

  // Application Components
  { title: 'Page headers', href: '/docs/application-components/page-headers', category: 'Application Components' },
  { title: 'Card headers', href: '/docs/application-components/card-headers', category: 'Application Components' },
  { title: 'Section headers ✨ NEW ✨', href: '/docs/application-components/section-headers', category: 'Application Components' },
  { title: 'Section footers', href: '/docs/application-components/section-footers', category: 'Application Components' },
  { title: 'Application navigation', href: '/docs/application-components/application-navigation', category: 'Application Components' },
  { title: 'Modals ✨ NEW ✨', href: '/docs/application-components/modals', category: 'Application Components' },
  { title: 'Command menus ✨ NEW ✨', href: '/docs/application-components/command-menus', category: 'Application Components' },
  { title: 'Charts ✨ NEW ✨', href: '/docs/application-components/charts', category: 'Application Components' },
  { title: 'Metrics ✨ NEW ✨', href: '/docs/application-components/metrics', category: 'Application Components' },
  { title: 'Slideout menus', href: '/docs/application-components/slideout-menus', category: 'Application Components' },
  { title: 'Inline CTAs', href: '/docs/application-components/inline-ctas', category: 'Application Components' },
  { title: 'Pagination', href: '/docs/application-components/pagination', category: 'Application Components' },
  { title: 'Progress steps ✨ NEW ✨', href: '/docs/application-components/progress-steps', category: 'Application Components' },
  { title: 'Activity feeds ✨ NEW ✨', href: '/docs/application-components/activity-feeds', category: 'Application Components' },
  { title: 'Messaging', href: '/docs/application-components/messaging', category: 'Application Components' },
  { title: 'Tabs ✨ NEW ✨', href: '/docs/application-components/tabs', category: 'Application Components' },
  { title: 'Tables', href: '/docs/application-components/tables', category: 'Application Components' },
  { title: 'Breadcrumbs', href: '/docs/application-components/breadcrumbs', category: 'Application Components' },
  { title: 'Alerts & notifications ✨ NEW ✨', href: '/docs/application-components/alerts-notifications', category: 'Application Components' },
  { title: 'Date pickers', href: '/docs/application-components/date-pickers', category: 'Application Components' },
  { title: 'File upload ✨ NEW ✨', href: '/docs/application-components/file-upload', category: 'Application Components' },
  { title: 'Content dividers', href: '/docs/application-components/content-dividers', category: 'Application Components' },
  { title: 'Loading indicators', href: '/docs/application-components/loading-indicators', category: 'Application Components' },
  { title: 'Empty states ✨ NEW ✨', href: '/docs/application-components/empty-states', category: 'Application Components' },
  { title: 'Code snippets', href: '/docs/application-components/code-snippets', category: 'Application Components' },
];

function generatePageTemplate(page) {
  const cleanTitle = page.title.replace(/✨ NEW ✨/g, '').trim();
  const slug = page.href.split('/').pop();
  const isNew = page.title.includes('✨ NEW ✨');

  return `'use client';

import DocsLayout from '@/components/DocsLayout';
import Link from 'next/link';

export default function ${slug.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('')}Page() {
  return (
    <DocsLayout>
      <div className="prose prose-lg max-w-none">
        {/* Header */}
        <div className="not-prose mb-8">
          <div className="flex items-center gap-3 mb-4">
            <h1 className="text-4xl font-bold text-gray-900">
              ${cleanTitle}
            </h1>
            ${isNew ? `<span className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium">
              ✨ NEW ✨
            </span>` : ''}
          </div>
          <p className="text-xl text-gray-600 leading-relaxed">
            ${getDescription(page.category, cleanTitle)}
          </p>
        </div>

        <h2 className="text-2xl font-bold text-gray-900 mb-4">Overview</h2>

        <p className="text-gray-600 mb-6">
          ${getOverviewContent(page.category, cleanTitle)}
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mb-4">Usage</h2>

        ${getUsageContent(page.category, cleanTitle)}

        <h2 className="text-2xl font-bold text-gray-900 mb-4">Examples</h2>

        <div className="not-prose grid grid-cols-1 md:grid-cols-2 gap-6 my-8">
          ${getExampleBlocks(page.category, cleanTitle)}
        </div>

        <h2 className="text-2xl font-bold text-gray-900 mb-4">Props & Variants</h2>

        ${getPropsContent(page.category, cleanTitle)}

        <h2 className="text-2xl font-bold text-gray-900 mb-4">Best Practices</h2>

        <ul className="list-disc list-inside text-gray-600 space-y-2 mb-6">
          ${getBestPractices(page.category, cleanTitle)}
        </ul>

        <h2 className="text-2xl font-bold text-gray-900 mb-4">Related Components</h2>

        <div className="not-prose bg-gray-50 rounded-lg p-6 mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Components</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            ${getRelatedComponents(page.category)}
          </div>
        </div>
      </div>
    </DocsLayout>
  );
}`;
}

function getDescription(category, title) {
  const descriptions = {
    'Foundations': `Essential design tokens and foundational elements for ${title.toLowerCase()}. These form the building blocks of our design system.`,
    'Shared Components': `Reusable ${title.toLowerCase()} components that can be used across different parts of your application.`,
    'Shared Assets': `${title} assets and templates that provide consistent visual elements across your projects.`,
    'Marketing Website Examples': `Complete ${title.toLowerCase()} examples and templates for marketing websites and landing pages.`,
    'Marketing Website Components': `${title} components specifically designed for marketing websites and promotional content.`,
    'Application Examples': `Full ${title.toLowerCase()} examples showcasing complex application interfaces and layouts.`,
    'Application Components': `Advanced ${title.toLowerCase()} components for building sophisticated application interfaces.`,
    'Getting Started': `${title} guide to help you get up and running with our design system quickly.`,
    'Thumbnail': `${title} and preview of the design system components and capabilities.`
  };
  return descriptions[category] || `Comprehensive guide to ${title.toLowerCase()} in our design system.`;
}

function getOverviewContent(category, title) {
  if (category === 'Foundations') {
    return `${title} are fundamental design tokens that ensure consistency across your application. They provide the visual foundation that all other components build upon.`;
  } else if (category.includes('Components')) {
    return `${title} are carefully crafted components that follow our design principles and accessibility guidelines. They're built with React Server Components for optimal performance.`;
  } else if (category.includes('Examples')) {
    return `${title} showcase complete implementations and best practices for building ${category.toLowerCase()}. Use these as starting points for your own implementations.`;
  }
  return `${title} provide essential functionality and visual consistency for your application.`;
}

function getUsageContent(category, title) {
  return `<div className="not-prose bg-gray-900 rounded-lg p-4 my-6">
          <pre className="text-green-400 text-sm overflow-x-auto">
            <code>{\`import { ${title.replace(/\s+/g, '')} } from '@your-org/design-system';

export function Example() {
  return (
    <${title.replace(/\s+/g, '')}
      variant="primary"
      size="md"
    >
      ${title} Example
    </${title.replace(/\s+/g, '')}>
  );
}\`}</code>
          </pre>
        </div>`;
}

function getExampleBlocks(category, title) {
  return `<div className="bg-white rounded-xl border border-gray-200 p-6">
            <h4 className="font-semibold text-gray-900 mb-3">Default ${title}</h4>
            <div className="bg-gray-50 rounded-lg p-4 flex items-center justify-center">
              <div className="text-gray-500 text-sm">Preview of ${title.toLowerCase()}</div>
            </div>
          </div>
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h4 className="font-semibold text-gray-900 mb-3">Variant Example</h4>
            <div className="bg-gray-50 rounded-lg p-4 flex items-center justify-center">
              <div className="text-gray-500 text-sm">Alternative ${title.toLowerCase()} style</div>
            </div>
          </div>`;
}

function getPropsContent(category, title) {
  return `<div className="not-prose overflow-x-auto mb-6">
          <table className="min-w-full border border-gray-200 rounded-lg">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-900">Prop</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-900">Type</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-900">Default</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-900">Description</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              <tr>
                <td className="px-4 py-2 text-sm text-gray-900">variant</td>
                <td className="px-4 py-2 text-sm text-gray-600">string</td>
                <td className="px-4 py-2 text-sm text-gray-600">"default"</td>
                <td className="px-4 py-2 text-sm text-gray-600">Visual style variant</td>
              </tr>
              <tr>
                <td className="px-4 py-2 text-sm text-gray-900">size</td>
                <td className="px-4 py-2 text-sm text-gray-600">string</td>
                <td className="px-4 py-2 text-sm text-gray-600">"md"</td>
                <td className="px-4 py-2 text-sm text-gray-600">Component size</td>
              </tr>
              <tr>
                <td className="px-4 py-2 text-sm text-gray-900">disabled</td>
                <td className="px-4 py-2 text-sm text-gray-600">boolean</td>
                <td className="px-4 py-2 text-sm text-gray-600">false</td>
                <td className="px-4 py-2 text-sm text-gray-600">Disable interaction</td>
              </tr>
              <tr>
                <td className="px-4 py-2 text-sm text-gray-900">className</td>
                <td className="px-4 py-2 text-sm text-gray-600">string</td>
                <td className="px-4 py-2 text-sm text-gray-600">-</td>
                <td className="px-4 py-2 text-sm text-gray-600">Additional CSS classes</td>
              </tr>
            </tbody>
          </table>
        </div>`;
}

function getBestPractices(category, title) {
  return `<li>Use ${title.toLowerCase()} consistently across your application</li>
          <li>Follow accessibility guidelines when implementing</li>
          <li>Test components across different screen sizes</li>
          <li>Maintain proper contrast ratios</li>
          <li>Use semantic HTML elements when possible</li>`;
}

function getRelatedComponents(category) {
  const related = {
    'Foundations': ['<Link href="/docs/foundations/colors">Colors</Link>', '<Link href="/docs/foundations/typography">Typography</Link>'],
    'Shared Components': ['<Link href="/docs/shared-components/buttons">Buttons</Link>', '<Link href="/docs/shared-components/inputs">Inputs</Link>'],
    'Marketing Website Components': ['<Link href="/docs/marketing-components/header-navigation">Header Navigation</Link>', '<Link href="/docs/marketing-components/footers">Footers</Link>'],
    'Application Components': ['<Link href="/docs/application-components/page-headers">Page Headers</Link>', '<Link href="/docs/application-components/tables">Tables</Link>']
  };
  return (related[category] || ['<Link href="/docs/introduction">Introduction</Link>', '<Link href="/docs/installation">Installation</Link>']).join('\n            ');
}

function createDirectoryStructure(filePath) {
  const dir = path.dirname(filePath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

function generateAllPages() {
  console.log('🚀 Generating all 94 documentation pages...');

  let generatedCount = 0;

  docsPages.forEach(page => {
    const filePath = path.join(__dirname, '..', 'app', page.href, 'page.tsx');
    const content = generatePageTemplate(page);

    createDirectoryStructure(filePath);
    fs.writeFileSync(filePath, content);

    generatedCount++;
    console.log(`✅ Generated: ${page.href}`);
  });

  console.log(`🎉 Successfully generated ${generatedCount} documentation pages!`);
}

// Run the generator
if (require.main === module) {
  generateAllPages();
}

module.exports = { generateAllPages, docsPages };
