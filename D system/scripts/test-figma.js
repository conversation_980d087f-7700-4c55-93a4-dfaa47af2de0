#!/usr/bin/env node

/**
 * Simple Figma API test script
 */

require('dotenv').config({ path: '.env.local' });

const FIGMA_ACCESS_TOKEN = process.env.FIGMA_ACCESS_TOKEN;
const FIGMA_FILE_ID = process.env.FIGMA_FILE_ID || 'CYlewnUysOEtOe6BVlSlZ3';

async function testFigmaConnection() {
  console.log('🔍 Testing Figma API connection...');
  console.log(`📁 File ID: ${FIGMA_FILE_ID}`);
  console.log(`🔑 Token: ${FIGMA_ACCESS_TOKEN ? 'Present' : 'Missing'}`);

  if (!FIGMA_ACCESS_TOKEN) {
    console.error('❌ No Figma access token found in environment variables');
    return;
  }

  try {
    console.log('📡 Making API request...');
    
    const response = await fetch(`https://api.figma.com/v1/files/${FIGMA_FILE_ID}`, {
      headers: {
        'X-Figma-Token': FIGMA_ACCESS_TOKEN,
      },
    });

    console.log(`📊 Response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ Successfully connected to Figma!');
    console.log(`📄 File name: ${data.name}`);
    console.log(`📅 Last modified: ${data.lastModified}`);
    console.log(`👤 Created by: ${data.role}`);
    
    if (data.document && data.document.children) {
      console.log(`📑 Pages found: ${data.document.children.length}`);
      data.document.children.forEach((page, index) => {
        console.log(`  ${index + 1}. ${page.name} (${page.children?.length || 0} frames)`);
      });
    }

    // Check for styles (design tokens)
    if (data.styles) {
      const styleCount = Object.keys(data.styles).length;
      console.log(`🎨 Styles found: ${styleCount}`);
      
      if (styleCount > 0) {
        console.log('📋 Style types:');
        const styleTypes = {};
        Object.values(data.styles).forEach(style => {
          styleTypes[style.styleType] = (styleTypes[style.styleType] || 0) + 1;
        });
        Object.entries(styleTypes).forEach(([type, count]) => {
          console.log(`  - ${type}: ${count}`);
        });
      }
    }

    // Check for components
    if (data.components) {
      const componentCount = Object.keys(data.components).length;
      console.log(`🧩 Components found: ${componentCount}`);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testFigmaConnection();
