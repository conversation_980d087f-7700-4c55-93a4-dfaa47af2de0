{"name": "design-system-rsc", "version": "1.0.0", "description": "A comprehensive design system built with Next.js 15, React 19, and Chakra UI 3.3", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "sync:figma": "node scripts/sync-figma.js", "type-check": "tsc --noEmit"}, "dependencies": {"@chakra-ui/react": "^3.19.1", "@emotion/react": "^11.14.0", "dotenv": "^16.5.0", "next": "^15.3.2", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/node": "^22.15.21", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "typescript": "^5.8.3"}, "keywords": ["design-system", "chakra-ui", "react", "nextjs", "figma", "rsc", "server-components"], "author": "Design System Team", "license": "MIT"}