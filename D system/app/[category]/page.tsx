import { notFound } from 'next/navigation';
import Layout from '@/components/Layout';
import { getCategoryById, designSystemCategories } from '@/lib/design-system';
import { DesignSystemCategory } from '@/types/design-system';
import Link from 'next/link';

interface CategoryPageProps {
  params: Promise<{
    category: DesignSystemCategory;
  }>;
}

// Generate static params for all categories
export async function generateStaticParams() {
  return designSystemCategories.map((category) => ({
    category: category.id,
  }));
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { category: categoryId } = await params;
  const category = getCategoryById(categoryId);

  if (!category) {
    notFound();
  }

  return (
    <Layout>
      <div className="flex flex-col gap-8">
        {/* Category Header */}
        <div>
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 bg-blue-50 rounded-lg text-blue-600">
              <svg className="w-6 h-6" viewBox="0 0 16 16" fill="currentColor">
                <path d="M4 2a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h8v8H4V4z"/>
              </svg>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {category.title}
              </h1>
              <p className="text-lg text-gray-600 mt-2">
                {category.description}
              </p>
            </div>
          </div>
          <div className="flex gap-4">
            <span className="badge badge-blue px-3 py-1">
              {category.components.length} Components
            </span>
            <span className="badge badge-green px-3 py-1">
              React Server Components
            </span>
          </div>
        </div>

        {/* Components Grid */}
        <div>
          <h2 className="text-xl font-bold text-gray-900 mb-6">
            Components
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {category.components.map((component) => (
              <Link
                key={component.id}
                href={`/${category.id}/${component.id}`}
              >
                <div className="card h-full cursor-pointer transition hover:shadow-lg hover:translate-y-[-2px]">
                  <div className="flex flex-col gap-4">
                    {/* Component Preview */}
                    <div className="w-full h-[120px] bg-gray-50 rounded border flex items-center justify-center">
                      <span className="text-sm text-gray-500">
                        Component Preview
                      </span>
                    </div>

                    {/* Component Info */}
                    <div className="w-full">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="text-base font-semibold text-gray-900">
                          {component.name}
                        </h3>
                        <span className="badge badge-gray">
                          {component.variants.length} variants
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 line-clamp-2">
                        {component.description}
                      </p>
                    </div>

                    {/* Variants Preview */}
                    {component.variants.length > 0 && (
                      <div className="w-full">
                        <p className="text-xs text-gray-500 mb-2">
                          Variants:
                        </p>
                        <div className="flex gap-2 flex-wrap">
                          {component.variants.slice(0, 3).map((variant) => (
                            <span
                              key={variant.name}
                              className="badge badge-blue"
                            >
                              {variant.name}
                            </span>
                          ))}
                          {component.variants.length > 3 && (
                            <span className="badge badge-gray">
                              +{component.variants.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Category Guidelines */}
        {category.id === 'foundations' && (
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-6">
              Guidelines
            </h2>
            <div className="card">
              <p className="text-gray-600">
                Foundations are the core building blocks of our design system. They include
                design tokens for colors, typography, spacing, and other fundamental elements
                that ensure consistency across all components and applications.
              </p>
            </div>
          </div>
        )}

        {category.id === 'shared-components' && (
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-6">
              Usage Guidelines
            </h2>
            <div className="card">
              <p className="text-gray-600">
                Shared components are reusable UI elements that can be used across different
                applications and contexts. They follow consistent patterns and behaviors
                while providing flexibility through props and variants.
              </p>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}
