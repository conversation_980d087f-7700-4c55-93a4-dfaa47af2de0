import { notFound } from 'next/navigation';
import Layout from '@/components/Layout';
import { getComponentById, getCategoryById } from '@/lib/design-system';
import { DesignSystemCategory } from '@/types/design-system';

interface ComponentPageProps {
  params: Promise<{
    category: DesignSystemCategory;
    component: string;
  }>;
}

// Component showcase components
function ComponentShowcase({ component }: { component: any }) {
  const renderComponent = (variant: any) => {
    // This is where you would render actual components based on the component type
    // For now, we'll show placeholder components
    switch (component.id) {
      case 'button':
        return (
          <button
            className={`btn ${variant.props.variant === 'outline' ? 'btn-secondary' : 'btn-primary'}`}
          >
            {variant.name} Button
          </button>
        );
      case 'input':
        return (
          <input
            className="input"
            placeholder={variant.props.placeholder || 'Enter text...'}
            disabled={variant.props.disabled}
          />
        );
      case 'colors':
        return (
          <div className="grid grid-cols-5 gap-4">
            {['blue', 'green', 'red', 'orange', 'purple'].map((color) => (
              <div key={color}>
                <div
                  className={`w-[60px] h-[60px] rounded mb-2`}
                  style={{ backgroundColor: `var(--color-${color}-500)` }}
                />
                <p className="text-sm text-center text-gray-500">
                  {color}.500
                </p>
              </div>
            ))}
          </div>
        );
      case 'typography':
        return (
          <div className="flex flex-col gap-4 items-start">
            <h1 className="text-4xl font-bold">Heading 1</h1>
            <h2 className="text-3xl font-bold">Heading 2</h2>
            <h3 className="text-2xl font-bold">Heading 3</h3>
            <h4 className="text-xl font-bold">Heading 4</h4>
            <p className="text-lg">Large body text</p>
            <p className="text-base">Regular body text</p>
            <p className="text-sm">Small text</p>
          </div>
        );
      case 'spacing':
        return (
          <div className="flex flex-col gap-4 items-start">
            {[1, 2, 4, 8, 16].map((space) => (
              <div key={space} className="flex items-center gap-4">
                <div className="w-[60px]">
                  <span className="text-sm text-gray-500">
                    {space * 4}px
                  </span>
                </div>
                <div
                  className="h-5 bg-blue-500 rounded-sm"
                  style={{ width: `${space * 4}px` }}
                />
              </div>
            ))}
          </div>
        );
      default:
        return (
          <div className="p-8 bg-gray-50 rounded border-2 border-dashed border-gray-300 text-center">
            <span className="text-gray-500">
              {component.name} - {variant.name} Variant
            </span>
          </div>
        );
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {component.variants.map((variant: any) => (
        <div key={variant.name} className="card">
          <div className="mb-4">
            <h3 className="text-base font-semibold">{variant.name}</h3>
            {variant.description && (
              <p className="text-sm text-gray-600">
                {variant.description}
              </p>
            )}
          </div>
          <div className="p-6 bg-gray-50 rounded border flex items-center justify-center min-h-[120px]">
            {renderComponent(variant)}
          </div>
        </div>
      ))}
    </div>
  );
}

export default async function ComponentPage({ params }: ComponentPageProps) {
  const { category: categoryId, component: componentId } = await params;
  const component = getComponentById(componentId);
  const category = getCategoryById(categoryId);

  if (!component || !category) {
    notFound();
  }

  const codeExample = `import { ${component.name} } from '@chakra-ui/react';

export function Example() {
  return (
    <${component.name}
      variant="solid"
      colorScheme="blue"
      size="md"
    >
      ${component.name} Example
    </${component.name}>
  );
}`;

  return (
    <Layout>
      <div className="flex flex-col gap-8">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <span>{category.title}</span>
          <span>/</span>
          <span className="text-gray-900 font-medium">
            {component.name}
          </span>
        </div>

        {/* Component Header */}
        <div>
          <div className="flex items-center gap-4 mb-4">
            <h1 className="text-3xl font-bold text-gray-900">
              {component.name}
            </h1>
            <span className="badge badge-blue px-3 py-1">
              {component.variants.length} variants
            </span>
            {component.figmaUrl && (
              <span className="badge badge-purple px-3 py-1">
                Figma Synced
              </span>
            )}
          </div>
          <p className="text-lg text-gray-600 max-w-3xl">
            {component.description}
          </p>
        </div>

        {/* Component Content */}
        <div>
          <div className="border-b border-gray-200 mb-6">
            <nav className="flex gap-8">
              <button className="py-2 px-1 border-b-2 border-blue-500 text-blue-600 font-medium">
                Preview
              </button>
              <button className="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                Code
              </button>
              <button className="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                Design Tokens
              </button>
              <button className="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                Figma
              </button>
            </nav>
          </div>

          {/* Preview Tab Content */}
          <div className="flex flex-col gap-6">
            <div>
              <h2 className="text-lg font-bold text-gray-900 mb-4">
                Variants
              </h2>
              <ComponentShowcase component={component} />
            </div>

            {/* Code Section */}
            <div>
              <h2 className="text-lg font-bold text-gray-900 mb-4">
                Usage Example
              </h2>
              <div className="card">
                <pre className="bg-gray-50 p-4 rounded text-sm font-mono overflow-x-auto">
                  {codeExample}
                </pre>
              </div>
            </div>

            {/* Props Section */}
            <div>
              <h2 className="text-lg font-bold text-gray-900 mb-4">
                Props
              </h2>
              <div className="card">
                <p className="text-gray-600">
                  Component props documentation would go here. This would include
                  all available props, their types, default values, and descriptions.
                </p>
              </div>
            </div>

            {/* Design Tokens Section */}
            <div>
              <h2 className="text-lg font-bold text-gray-900 mb-4">
                Design Tokens
              </h2>
              <div className="card">
                <p className="text-gray-600 mb-4">
                  Design tokens used by this component:
                </p>
                <div className="flex flex-col gap-3">
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="font-medium">--chakra-colors-blue-500</span>
                    <code className="design-token">#3182ce</code>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="font-medium">--chakra-space-4</span>
                    <code className="design-token">16px</code>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="font-medium">--chakra-radii-md</span>
                    <code className="design-token">6px</code>
                  </div>
                </div>
              </div>
            </div>

            {/* Figma Section */}
            <div>
              <h2 className="text-lg font-bold text-gray-900 mb-4">
                Figma Integration
              </h2>
              <div className="card">
                {component.figmaUrl ? (
                  <div className="flex flex-col gap-4">
                    <p className="text-gray-600">
                      This component is synced with Figma. Design changes will be
                      automatically reflected in the component library.
                    </p>
                    <div className="w-full h-[300px] bg-gray-50 rounded border flex items-center justify-center">
                      <span className="text-gray-500">
                        Figma embed would appear here
                      </span>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-600">
                    This component is not yet connected to Figma. Connect it to enable
                    automatic design sync and token extraction.
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
