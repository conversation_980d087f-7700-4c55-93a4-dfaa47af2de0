import Layout from '@/components/Layout';
import { createFigmaSync, mockFigmaComponents, mockFigmaTokens } from '@/lib/figma-sync';
import { FIGMA_FILE_ID } from '@/lib/figma-integration';

export default async function FigmaSyncPage() {
  // In a real implementation, you would use the actual Figma API
  // For now, we'll use mock data to demonstrate the structure
  const figmaSync = createFigmaSync(process.env.FIGMA_ACCESS_TOKEN);
  
  // Mock synced components and tokens
  const syncedComponents = mockFigmaComponents;
  const syncedTokens = mockFigmaTokens;

  return (
    <Layout>
      <div className="flex flex-col gap-8">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Figma Integration
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            Real-time synchronization with the Figma design system file. Components and design tokens 
            are automatically synced to maintain consistency between design and code.
          </p>
          <div className="flex gap-4">
            <span className="badge badge-green px-3 py-1">
              Connected to Figma
            </span>
            <span className="badge badge-blue px-3 py-1">
              Auto-sync Enabled
            </span>
          </div>
        </div>

        {/* Figma File Info */}
        <div className="card">
          <h2 className="text-xl font-bold text-gray-900 mb-4">
            Connected Figma File
          </h2>
          <div className="flex flex-col gap-4">
            <div className="flex justify-between items-center">
              <span className="font-medium text-gray-700">File ID:</span>
              <code className="design-token">{FIGMA_FILE_ID}</code>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium text-gray-700">File URL:</span>
              <a 
                href={`https://www.figma.com/design/${FIGMA_FILE_ID}/Design-System`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-700 underline"
              >
                Open in Figma
              </a>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium text-gray-700">Last Sync:</span>
              <span className="text-gray-600">Just now</span>
            </div>
          </div>
        </div>

        {/* Synced Components */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Synced Components
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {syncedComponents.map((component) => (
              <div key={component.id} className="card">
                <div className="flex flex-col gap-4">
                  {/* Component Preview */}
                  <div className="w-full h-[120px] bg-gray-50 rounded border flex items-center justify-center">
                    <span className="text-sm text-gray-500">
                      Figma Component Preview
                    </span>
                  </div>

                  {/* Component Info */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="text-base font-semibold text-gray-900">
                        {component.name}
                      </h3>
                      <span className="badge badge-purple">
                        Figma Synced
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">
                      {component.description}
                    </p>
                    
                    {/* Figma Link */}
                    {component.figmaUrl && (
                      <a 
                        href={component.figmaUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-700 text-sm underline"
                      >
                        View in Figma →
                      </a>
                    )}
                  </div>

                  {/* Variants */}
                  <div>
                    <p className="text-xs text-gray-500 mb-2">
                      Variants ({component.variants.length}):
                    </p>
                    <div className="flex gap-2 flex-wrap">
                      {component.variants.slice(0, 3).map((variant) => (
                        <span
                          key={variant.name}
                          className="badge badge-blue"
                        >
                          {variant.name}
                        </span>
                      ))}
                      {component.variants.length > 3 && (
                        <span className="badge badge-gray">
                          +{component.variants.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Design Tokens */}
                  {component.tokens && component.tokens.length > 0 && (
                    <div>
                      <p className="text-xs text-gray-500 mb-2">
                        Design Tokens ({component.tokens.length}):
                      </p>
                      <div className="flex flex-col gap-1">
                        {component.tokens.slice(0, 2).map((token) => (
                          <div key={token.name} className="flex justify-between items-center text-xs">
                            <span className="text-gray-600">{token.name}</span>
                            <code className="text-xs bg-gray-100 px-1 rounded">{token.value}</code>
                          </div>
                        ))}
                        {component.tokens.length > 2 && (
                          <span className="text-xs text-gray-500">
                            +{component.tokens.length - 2} more tokens
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Design Tokens */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Design Tokens
          </h2>
          <div className="card">
            <p className="text-gray-600 mb-4">
              Design tokens automatically extracted from Figma styles and components:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {syncedTokens.map((token) => (
                <div key={token.name} className="p-3 bg-gray-50 rounded border">
                  <div className="flex justify-between items-center mb-1">
                    <span className="font-medium text-sm text-gray-900">
                      {token.name}
                    </span>
                    <span className={`badge badge-${token.type === 'color' ? 'blue' : token.type === 'typography' ? 'green' : 'gray'}`}>
                      {token.type}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <code className="text-xs bg-white px-2 py-1 rounded border">
                      {token.value}
                    </code>
                    {token.type === 'color' && (
                      <div 
                        className="w-4 h-4 rounded border"
                        style={{ backgroundColor: token.value }}
                      />
                    )}
                  </div>
                  {token.description && (
                    <p className="text-xs text-gray-500 mt-1">
                      {token.description}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sync Actions */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Sync Actions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Manual Sync
              </h3>
              <p className="text-gray-600 mb-4">
                Manually trigger a sync to pull the latest changes from Figma.
              </p>
              <button className="btn btn-primary">
                Sync Now
              </button>
            </div>
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Export Tokens
              </h3>
              <p className="text-gray-600 mb-4">
                Export design tokens as CSS variables or Chakra UI theme.
              </p>
              <div className="flex gap-2">
                <button className="btn btn-secondary">
                  Export CSS
                </button>
                <button className="btn btn-secondary">
                  Export Theme
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Integration Guide */}
        <div className="card">
          <h2 className="text-xl font-bold text-gray-900 mb-4">
            Setting Up Figma Integration
          </h2>
          <div className="prose prose-sm text-gray-600">
            <ol className="list-decimal list-inside space-y-2">
              <li>Generate a Figma access token from your Figma account settings</li>
              <li>Add the token to your <code>.env.local</code> file as <code>FIGMA_ACCESS_TOKEN</code></li>
              <li>Ensure your Figma file follows the design system naming conventions</li>
              <li>Components will automatically sync and update the RSC library</li>
              <li>Design tokens are extracted from Figma styles and component properties</li>
            </ol>
          </div>
        </div>
      </div>
    </Layout>
  );
}
