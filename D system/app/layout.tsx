import type { Metadata } from 'next';
import './globals.css';

export const metadata: Metadata = {
  title: 'Design System - Chakra UI RSC Library',
  description: 'A comprehensive design system built with Next.js 15, React 19, and Chakra UI 3.3',
  keywords: ['design system', 'chakra ui', 'react', 'next.js', 'figma', 'components'],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        {children}
      </body>
    </html>
  );
}
