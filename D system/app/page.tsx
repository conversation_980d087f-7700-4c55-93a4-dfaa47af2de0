import Layout from '@/components/Layout';
import { designSystemCategories } from '@/lib/design-system';
import Link from 'next/link';

export default function HomePage() {
  const totalComponents = designSystemCategories.reduce(
    (total, category) => total + category.components.length,
    0
  );

  return (
    <Layout>
      <div className="flex flex-col gap-8">
        {/* Hero Section */}
        <div className="text-center py-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Design System
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-6">
            A comprehensive design system built with Next.js 15, React 19, and Chakra UI 3.3.
            Featuring Figma integration and React Server Components.
          </p>
          <div className="flex gap-4 justify-center">
            <span className="badge badge-blue px-3 py-1">
              Next.js 15
            </span>
            <span className="badge badge-green px-3 py-1">
              React 19
            </span>
            <span className="badge badge-purple px-3 py-1">
              Chakra UI 3.3
            </span>
            <span className="badge badge-orange px-3 py-1">
              Figma Integration
            </span>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="card text-center py-6">
            <div className="text-3xl font-bold text-blue-600">
              {designSystemCategories.length}
            </div>
            <div className="text-gray-500">Categories</div>
          </div>
          <div className="card text-center py-6">
            <div className="text-3xl font-bold text-blue-600">
              {totalComponents}
            </div>
            <div className="text-gray-500">Components</div>
          </div>
          <div className="card text-center py-6">
            <div className="text-3xl font-bold text-blue-600">
              RSC
            </div>
            <div className="text-gray-500">Server Components</div>
          </div>
        </div>

        {/* Categories Overview */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Categories
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {designSystemCategories.map((category) => (
              <Link key={category.id} href={`/${category.id}`}>
                <div className="card h-full cursor-pointer transition hover:shadow-lg hover:translate-y-[-2px]">
                  <div className="flex flex-col gap-3">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-50 rounded text-blue-600">
                        <svg className="w-5 h-5" viewBox="0 0 16 16" fill="currentColor">
                          <path d="M4 2a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h8v8H4V4z"/>
                        </svg>
                      </div>
                      <span className="badge badge-gray">
                        {category.components.length} components
                      </span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {category.title}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {category.description}
                      </p>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Getting Started */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Getting Started
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Explore Components
              </h3>
              <p className="text-gray-600 mb-4">
                Browse through our comprehensive collection of components organized by category.
                Each component includes multiple variants and usage examples.
              </p>
              <Link href="/foundations" className="text-blue-600 font-medium hover:text-blue-700">
                Start with Foundations →
              </Link>
            </div>
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Figma Integration
              </h3>
              <p className="text-gray-600 mb-4">
                Our design system is connected to Figma, allowing you to sync design tokens
                and convert designs into React Server Components.
              </p>
              <Link href="/shared-components" className="text-blue-600 font-medium hover:text-blue-700">
                View Components →
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
