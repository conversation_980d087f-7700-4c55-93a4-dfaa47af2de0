'use client';

import DocsLayout from '@/components/DocsLayout';
import Link from 'next/link';

export default function HeaderSectionsPage() {
  return (
    <DocsLayout>
      <div className="prose prose-lg max-w-none">
        {/* Header */}
        <div className="not-prose mb-8">
          <div className="flex items-center gap-3 mb-4">
            <h1 className="text-4xl font-bold text-gray-900">
              Header sections
            </h1>
            
          </div>
          <p className="text-xl text-gray-600 leading-relaxed">
            Header sections components specifically designed for marketing websites and promotional content.
          </p>
        </div>

        <h2 className="text-2xl font-bold text-gray-900 mb-4">Overview</h2>

        <p className="text-gray-600 mb-6">
          Header sections are carefully crafted components that follow our design principles and accessibility guidelines. They're built with React Server Components for optimal performance.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mb-4">Usage</h2>

        <div className="not-prose bg-gray-900 rounded-lg p-4 my-6">
          <pre className="text-green-400 text-sm overflow-x-auto">
            <code>{`import { Headersections } from '@your-org/design-system';

export function Example() {
  return (
    <Headersections
      variant="primary"
      size="md"
    >
      Header sections Example
    </Headersections>
  );
}`}</code>
          </pre>
        </div>

        <h2 className="text-2xl font-bold text-gray-900 mb-4">Examples</h2>

        <div className="not-prose grid grid-cols-1 md:grid-cols-2 gap-6 my-8">
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h4 className="font-semibold text-gray-900 mb-3">Default Header sections</h4>
            <div className="bg-gray-50 rounded-lg p-4 flex items-center justify-center">
              <div className="text-gray-500 text-sm">Preview of header sections</div>
            </div>
          </div>
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h4 className="font-semibold text-gray-900 mb-3">Variant Example</h4>
            <div className="bg-gray-50 rounded-lg p-4 flex items-center justify-center">
              <div className="text-gray-500 text-sm">Alternative header sections style</div>
            </div>
          </div>
        </div>

        <h2 className="text-2xl font-bold text-gray-900 mb-4">Props & Variants</h2>

        <div className="not-prose overflow-x-auto mb-6">
          <table className="min-w-full border border-gray-200 rounded-lg">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-900">Prop</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-900">Type</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-900">Default</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-900">Description</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              <tr>
                <td className="px-4 py-2 text-sm text-gray-900">variant</td>
                <td className="px-4 py-2 text-sm text-gray-600">string</td>
                <td className="px-4 py-2 text-sm text-gray-600">"default"</td>
                <td className="px-4 py-2 text-sm text-gray-600">Visual style variant</td>
              </tr>
              <tr>
                <td className="px-4 py-2 text-sm text-gray-900">size</td>
                <td className="px-4 py-2 text-sm text-gray-600">string</td>
                <td className="px-4 py-2 text-sm text-gray-600">"md"</td>
                <td className="px-4 py-2 text-sm text-gray-600">Component size</td>
              </tr>
              <tr>
                <td className="px-4 py-2 text-sm text-gray-900">disabled</td>
                <td className="px-4 py-2 text-sm text-gray-600">boolean</td>
                <td className="px-4 py-2 text-sm text-gray-600">false</td>
                <td className="px-4 py-2 text-sm text-gray-600">Disable interaction</td>
              </tr>
              <tr>
                <td className="px-4 py-2 text-sm text-gray-900">className</td>
                <td className="px-4 py-2 text-sm text-gray-600">string</td>
                <td className="px-4 py-2 text-sm text-gray-600">-</td>
                <td className="px-4 py-2 text-sm text-gray-600">Additional CSS classes</td>
              </tr>
            </tbody>
          </table>
        </div>

        <h2 className="text-2xl font-bold text-gray-900 mb-4">Best Practices</h2>

        <ul className="list-disc list-inside text-gray-600 space-y-2 mb-6">
          <li>Use header sections consistently across your application</li>
          <li>Follow accessibility guidelines when implementing</li>
          <li>Test components across different screen sizes</li>
          <li>Maintain proper contrast ratios</li>
          <li>Use semantic HTML elements when possible</li>
        </ul>

        <h2 className="text-2xl font-bold text-gray-900 mb-4">Related Components</h2>

        <div className="not-prose bg-gray-50 rounded-lg p-6 mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Components</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <Link href="/docs/marketing-components/header-navigation">Header Navigation</Link>
            <Link href="/docs/marketing-components/footers">Footers</Link>
          </div>
        </div>
      </div>
    </DocsLayout>
  );
}