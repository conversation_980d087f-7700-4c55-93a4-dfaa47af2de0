import DocsLayout from '@/components/DocsLayout';
import Link from 'next/link';

export default function IntroductionPage() {
  return (
    <DocsLayout>
      <div className="prose prose-lg max-w-none">
        {/* Header */}
        <div className="not-prose mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Introduction
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed">
            Welcome to our comprehensive design system built with Next.js 15, React 19, and Chakra UI 3.3.
            This documentation will guide you through all components, patterns, and best practices.
          </p>
        </div>

        {/* Quick Start Cards */}
        <div className="not-prose grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-6 text-white">
            <div className="flex items-center mb-4">
              <svg className="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <h3 className="text-lg font-semibold">Quick Start</h3>
            </div>
            <p className="text-purple-100 mb-4">
              Get up and running with our design system in minutes.
            </p>
            <Link href="/docs/quick-start" className="inline-flex items-center text-white font-medium hover:text-purple-100">
              Get Started
              <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>

          <div className="bg-gradient-to-r from-teal-500 to-teal-600 rounded-xl p-6 text-white">
            <div className="flex items-center mb-4">
              <svg className="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              <h3 className="text-lg font-semibold">Figma Integration</h3>
            </div>
            <p className="text-teal-100 mb-4">
              Seamlessly sync components and design tokens from Figma.
            </p>
            <Link href="/figma-sync" className="inline-flex items-center text-white font-medium hover:text-teal-100">
              View Integration
              <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>

        ## What is this Design System?

        Our design system is a comprehensive collection of reusable components, design tokens, and guidelines built with modern web technologies. It provides a consistent foundation for building beautiful, accessible, and performant user interfaces.

        ### Key Features

        - **🚀 Next.js 15** - Built with the latest Next.js features including App Router and React Server Components
        - **⚛️ React 19** - Leverages the newest React features for optimal performance
        - **🎨 Chakra UI 3.3** - Integrated with Chakra UI for consistent styling and theming
        - **🔗 Figma Integration** - Real-time sync with Figma design files
        - **📱 Responsive** - Mobile-first design with responsive components
        - **♿ Accessible** - Built with accessibility best practices
        - **🔧 TypeScript** - Full TypeScript support for type safety

        ## Architecture

        Our design system follows a modular architecture with clear separation of concerns:

        <div className="not-prose bg-gray-900 rounded-lg p-4 my-6">
          <pre className="text-green-400 text-sm overflow-x-auto">
            <code>{`Design System
├── Foundations
│   ├── Colors & Typography
│   ├── Spacing & Layout
│   └── Icons & Assets
├── Components
│   ├── Shared Components
│   ├── Marketing Components
│   └── Application Components
└── Patterns
    ├── Page Templates
    └── Component Compositions`}</code>
          </pre>
        </div>

        ## Getting Started

        ### Installation

        <div className="not-prose bg-gray-900 rounded-lg p-4 my-6">
          <pre className="text-green-400 text-sm overflow-x-auto">
            <code>{`npm install @your-org/design-system
# or
yarn add @your-org/design-system`}</code>
          </pre>
        </div>

        ### Basic Usage

        <div className="not-prose bg-gray-900 rounded-lg p-4 my-6">
          <pre className="text-green-400 text-sm overflow-x-auto">
            <code>{`import { Button, Card } from '@your-org/design-system';

export function MyComponent() {
  return (
    <Card>
      <Button variant="primary" size="lg">
        Get Started
      </Button>
    </Card>
  );
}`}</code>
          </pre>
        </div>

        ## Component Categories

        <div className="not-prose grid grid-cols-1 md:grid-cols-2 gap-4 my-8">
          <Link href="/docs/foundations/colors" className="group block p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:shadow-md transition-all">
            <div className="flex items-center mb-2">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"/>
                  <path fillRule="evenodd" d="M3 8a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 group-hover:text-purple-600">Foundations</h3>
            </div>
            <p className="text-sm text-gray-600">Colors, typography, spacing, and other design tokens</p>
          </Link>

          <Link href="/docs/shared-components/buttons" className="group block p-4 border border-gray-200 rounded-lg hover:border-teal-300 hover:shadow-md transition-all">
            <div className="flex items-center mb-2">
              <div className="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-4 h-4 text-teal-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 group-hover:text-teal-600">Shared Components</h3>
            </div>
            <p className="text-sm text-gray-600">Reusable UI components like buttons, inputs, and cards</p>
          </Link>

          <Link href="/docs/marketing-components/header-navigation" className="group block p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:shadow-md transition-all">
            <div className="flex items-center mb-2">
              <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-4 h-4 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4z"/>
                  <path fillRule="evenodd" d="M3 8a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 group-hover:text-indigo-600">Marketing Components</h3>
            </div>
            <p className="text-sm text-gray-600">Components for marketing websites and landing pages</p>
          </Link>

          <Link href="/docs/application-components/page-headers" className="group block p-4 border border-gray-200 rounded-lg hover:border-pink-300 hover:shadow-md transition-all">
            <div className="flex items-center mb-2">
              <div className="w-8 h-8 bg-pink-100 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-4 h-4 text-pink-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 group-hover:text-pink-600">Application Components</h3>
            </div>
            <p className="text-sm text-gray-600">Complex components for application interfaces</p>
          </Link>
        </div>

        ## Design Principles

        Our design system is built on these core principles:

        ### Consistency
        Maintain visual and functional consistency across all components and patterns.

        ### Accessibility
        Ensure all components meet WCAG 2.1 AA standards for accessibility.

        ### Performance
        Optimize for performance with React Server Components and efficient rendering.

        ### Scalability
        Design components that scale from simple websites to complex applications.

        ## Next Steps

        <div className="not-prose bg-gray-50 rounded-lg p-6 mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Ready to get started?</h3>
          <div className="flex flex-col sm:flex-row gap-3">
            <Link href="/docs/installation" className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors">
              Installation Guide
              <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
            <Link href="/docs/foundations/colors" className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors">
              Explore Foundations
            </Link>
          </div>
        </div>
      </div>
    </DocsLayout>
  );
}
