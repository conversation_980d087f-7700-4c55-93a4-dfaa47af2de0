'use client';

import DocsLayout from '@/components/DocsLayout';
import Link from 'next/link';

export default function BlogPostsPage() {
  return (
    <DocsLayout>
      <div className="prose prose-lg max-w-none">
        {/* Header */}
        <div className="not-prose mb-8">
          <div className="flex items-center gap-3 mb-4">
            <h1 className="text-4xl font-bold text-gray-900">
              Blog posts
            </h1>
            
          </div>
          <p className="text-xl text-gray-600 leading-relaxed">
            Complete blog posts examples and templates for marketing websites and landing pages.
          </p>
        </div>

        ## Overview

        Blog posts showcase complete implementations and best practices for building marketing website examples. Use these as starting points for your own implementations.

        ## Usage

        ```tsx
import { Blogposts } from '@your-org/design-system';

export function Example() {
  return (
    <Blogposts
      variant="primary"
      size="md"
    >
      Blog posts Example
    </Blogposts>
  );
}
```

        ## Examples

        <div className="not-prose grid grid-cols-1 md:grid-cols-2 gap-6 my-8">
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h4 className="font-semibold text-gray-900 mb-3">Default Blog posts</h4>
            <div className="bg-gray-50 rounded-lg p-4 flex items-center justify-center">
              <div className="text-gray-500 text-sm">Preview of blog posts</div>
            </div>
          </div>
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h4 className="font-semibold text-gray-900 mb-3">Variant Example</h4>
            <div className="bg-gray-50 rounded-lg p-4 flex items-center justify-center">
              <div className="text-gray-500 text-sm">Alternative blog posts style</div>
            </div>
          </div>
        </div>

        ## Props & Variants

        | Prop | Type | Default | Description |
|------|------|---------|-------------|
| variant | string | "default" | Visual style variant |
| size | string | "md" | Component size |
| disabled | boolean | false | Disable interaction |
| className | string | - | Additional CSS classes |

        ## Best Practices

        - Use blog posts consistently across your application
- Follow accessibility guidelines when implementing
- Test components across different screen sizes
- Maintain proper contrast ratios
- Use semantic HTML elements when possible

        ## Related Components

        <div className="not-prose bg-gray-50 rounded-lg p-6 mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Components</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <Link href="/docs/introduction">Introduction</Link>
            <Link href="/docs/installation">Installation</Link>
          </div>
        </div>
      </div>
    </DocsLayout>
  );
}