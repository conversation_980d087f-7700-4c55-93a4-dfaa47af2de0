'use client';

import DocsLayout from '@/components/DocsLayout';
import Link from 'next/link';

export default function SectionFootersPage() {
  return (
    <DocsLayout>
      <div className="prose prose-lg max-w-none">
        {/* Header */}
        <div className="not-prose mb-8">
          <div className="flex items-center gap-3 mb-4">
            <h1 className="text-4xl font-bold text-gray-900">
              Section footers
            </h1>
            
          </div>
          <p className="text-xl text-gray-600 leading-relaxed">
            Advanced section footers components for building sophisticated application interfaces.
          </p>
        </div>

        ## Overview

        Section footers are carefully crafted components that follow our design principles and accessibility guidelines. They're built with React Server Components for optimal performance.

        ## Usage

        ```tsx
import { Sectionfooters } from '@your-org/design-system';

export function Example() {
  return (
    <Sectionfooters
      variant="primary"
      size="md"
    >
      Section footers Example
    </Sectionfooters>
  );
}
```

        ## Examples

        <div className="not-prose grid grid-cols-1 md:grid-cols-2 gap-6 my-8">
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h4 className="font-semibold text-gray-900 mb-3">Default Section footers</h4>
            <div className="bg-gray-50 rounded-lg p-4 flex items-center justify-center">
              <div className="text-gray-500 text-sm">Preview of section footers</div>
            </div>
          </div>
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h4 className="font-semibold text-gray-900 mb-3">Variant Example</h4>
            <div className="bg-gray-50 rounded-lg p-4 flex items-center justify-center">
              <div className="text-gray-500 text-sm">Alternative section footers style</div>
            </div>
          </div>
        </div>

        ## Props & Variants

        | Prop | Type | Default | Description |
|------|------|---------|-------------|
| variant | string | "default" | Visual style variant |
| size | string | "md" | Component size |
| disabled | boolean | false | Disable interaction |
| className | string | - | Additional CSS classes |

        ## Best Practices

        - Use section footers consistently across your application
- Follow accessibility guidelines when implementing
- Test components across different screen sizes
- Maintain proper contrast ratios
- Use semantic HTML elements when possible

        ## Related Components

        <div className="not-prose bg-gray-50 rounded-lg p-6 mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Components</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <Link href="/docs/application-components/page-headers">Page Headers</Link>
            <Link href="/docs/application-components/tables">Tables</Link>
          </div>
        </div>
      </div>
    </DocsLayout>
  );
}