'use client';

import DocsLayout from '@/components/DocsLayout';
import Link from 'next/link';

export default function GridsSpacingPage() {
  return (
    <DocsLayout>
      <div className="prose prose-lg max-w-none">
        {/* Header */}
        <div className="not-prose mb-8">
          <div className="flex items-center gap-3 mb-4">
            <h1 className="text-4xl font-bold text-gray-900">
              Grids & spacing
            </h1>
            
          </div>
          <p className="text-xl text-gray-600 leading-relaxed">
            Essential design tokens and foundational elements for grids & spacing. These form the building blocks of our design system.
          </p>
        </div>

        ## Overview

        Grids & spacing are fundamental design tokens that ensure consistency across your application. They provide the visual foundation that all other components build upon.

        ## Usage

        ```tsx
import { Grids&spacing } from '@your-org/design-system';

export function Example() {
  return (
    <Grids&spacing
      variant="primary"
      size="md"
    >
      Grids & spacing Example
    </Grids&spacing>
  );
}
```

        ## Examples

        <div className="not-prose grid grid-cols-1 md:grid-cols-2 gap-6 my-8">
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h4 className="font-semibold text-gray-900 mb-3">Default Grids & spacing</h4>
            <div className="bg-gray-50 rounded-lg p-4 flex items-center justify-center">
              <div className="text-gray-500 text-sm">Preview of grids & spacing</div>
            </div>
          </div>
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h4 className="font-semibold text-gray-900 mb-3">Variant Example</h4>
            <div className="bg-gray-50 rounded-lg p-4 flex items-center justify-center">
              <div className="text-gray-500 text-sm">Alternative grids & spacing style</div>
            </div>
          </div>
        </div>

        ## Props & Variants

        | Prop | Type | Default | Description |
|------|------|---------|-------------|
| variant | string | "default" | Visual style variant |
| size | string | "md" | Component size |
| disabled | boolean | false | Disable interaction |
| className | string | - | Additional CSS classes |

        ## Best Practices

        - Use grids & spacing consistently across your application
- Follow accessibility guidelines when implementing
- Test components across different screen sizes
- Maintain proper contrast ratios
- Use semantic HTML elements when possible

        ## Related Components

        <div className="not-prose bg-gray-50 rounded-lg p-6 mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Components</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <Link href="/docs/foundations/colors">Colors</Link>
            <Link href="/docs/foundations/typography">Typography</Link>
          </div>
        </div>
      </div>
    </DocsLayout>
  );
}