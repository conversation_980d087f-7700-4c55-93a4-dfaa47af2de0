// Design System Types for Figma Integration and RSC

export interface DesignToken {
  name: string;
  value: string | number;
  type: 'color' | 'spacing' | 'typography' | 'shadow' | 'border' | 'size';
  description?: string;
  figmaId?: string;
}

export interface ComponentVariant {
  name: string;
  props: Record<string, any>;
  description?: string;
}

export interface DesignSystemComponent {
  id: string;
  name: string;
  category: DesignSystemCategory;
  description: string;
  variants: ComponentVariant[];
  figmaUrl?: string;
  codeExample?: string;
  tokens?: DesignToken[];
}

export type DesignSystemCategory = 
  | 'thumbnail'
  | 'foundations'
  | 'shared-components'
  | 'shared-assets'
  | 'marketing-website-examples'
  | 'marketing-website-components'
  | 'application-examples'
  | 'application-components';

export interface CategoryConfig {
  id: DesignSystemCategory;
  title: string;
  description: string;
  icon?: string;
  components: DesignSystemComponent[];
}

export interface FigmaIntegration {
  fileId: string;
  nodeId: string;
  accessToken?: string;
}

export interface RSCProps {
  children?: React.ReactNode;
  className?: string;
  variant?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  colorScheme?: string;
}

// Figma API Types
export interface FigmaNode {
  id: string;
  name: string;
  type: string;
  children?: FigmaNode[];
  fills?: FigmaFill[];
  strokes?: FigmaStroke[];
  effects?: FigmaEffect[];
  constraints?: FigmaConstraints;
  absoluteBoundingBox?: FigmaBoundingBox;
}

export interface FigmaFill {
  type: string;
  color?: FigmaColor;
  gradientStops?: FigmaGradientStop[];
}

export interface FigmaColor {
  r: number;
  g: number;
  b: number;
  a: number;
}

export interface FigmaStroke {
  type: string;
  color: FigmaColor;
  weight: number;
}

export interface FigmaEffect {
  type: string;
  color?: FigmaColor;
  offset?: { x: number; y: number };
  radius?: number;
  spread?: number;
}

export interface FigmaConstraints {
  vertical: string;
  horizontal: string;
}

export interface FigmaBoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface FigmaGradientStop {
  position: number;
  color: FigmaColor;
}

// Navigation Types
export interface NavigationItem {
  id: string;
  title: string;
  href: string;
  category: DesignSystemCategory;
  children?: NavigationItem[];
}

export interface SidebarProps {
  categories: CategoryConfig[];
  currentPath?: string;
}
