'use client';

import { Box, Text, VStack, HStack, <PERSON>, Button } from '@chakra-ui/react';
import { useState } from 'react';

interface ComponentPreviewProps {
  component: any;
  variant: any;
  showCode?: boolean;
}

export default function ComponentPreview({ 
  component, 
  variant, 
  showCode = false 
}: ComponentPreviewProps) {
  const [isCodeVisible, setIsCodeVisible] = useState(showCode);

  const renderPreview = () => {
    // This would be expanded to render actual components
    // For now, showing placeholder based on component type
    switch (component.id) {
      case 'button':
        return (
          <Button
            variant={variant.props.variant || 'solid'}
            colorScheme={variant.props.colorScheme || 'blue'}
            size="md"
          >
            {variant.name} Button
          </Button>
        );
      default:
        return (
          <Box
            p={4}
            bg="bg.subtle"
            borderRadius="md"
            border="1px dashed"
            borderColor="border.subtle"
            textAlign="center"
          >
            <Text color="fg.muted">
              {component.name} - {variant.name}
            </Text>
          </Box>
        );
    }
  };

  const generateCode = () => {
    const props = Object.entries(variant.props)
      .map(([key, value]) => `${key}="${value}"`)
      .join(' ');

    return `<${component.name}${props ? ' ' + props : ''}>${variant.name}</${component.name}>`;
  };

  return (
    <VStack spacing={4} align="stretch">
      {/* Preview */}
      <Box
        p={6}
        bg="bg.canvas"
        borderRadius="md"
        border="1px solid"
        borderColor="border.subtle"
        display="flex"
        alignItems="center"
        justifyContent="center"
        minHeight="120px"
      >
        {renderPreview()}
      </Box>

      {/* Code Toggle */}
      <HStack justify="space-between">
        <Text fontSize="sm" color="fg.muted">
          {variant.name} variant
        </Text>
        <Button
          size="sm"
          variant="ghost"
          onClick={() => setIsCodeVisible(!isCodeVisible)}
        >
          {isCodeVisible ? 'Hide Code' : 'Show Code'}
        </Button>
      </HStack>

      {/* Code */}
      {isCodeVisible && (
        <Code
          display="block"
          p={3}
          bg="bg.subtle"
          borderRadius="md"
          fontSize="sm"
          fontFamily="mono"
        >
          {generateCode()}
        </Code>
      )}
    </VStack>
  );
}
