'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { designSystemCategories } from '@/lib/design-system';
import { SidebarProps } from '@/types/design-system';

// Icons for categories (using simple SVG icons)
const CategoryIcons = {
  thumbnail: (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M2 3a1 1 0 011-1h10a1 1 0 011 1v10a1 1 0 01-1 1H3a1 1 0 01-1-1V3zm2 1v8h8V4H4z"/>
    </svg>
  ),
  foundations: (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M8 1l7 4v6l-7 4-7-4V5l7-4zm0 2L3.5 5.5 8 8l4.5-2.5L8 3zm-5 4v4l5 2.5V9.5L3 7zm10 0L8 9.5V14l5-2.5V7z"/>
    </svg>
  ),
  'shared-components': (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M4 2a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h8v8H4V4z"/>
    </svg>
  ),
  'shared-assets': (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M8 1a7 7 0 100 14A7 7 0 008 1zM6 6a1 1 0 112 0 1 1 0 01-2 0zm1 3a1 1 0 100 2 1 1 0 000-2z"/>
    </svg>
  ),
  'marketing-website-examples': (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M1 2a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H2a1 1 0 01-1-1V2zm2 1v10h10V3H3z"/>
    </svg>
  ),
  'marketing-website-components': (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M2 2h12v2H2V2zm0 4h12v2H2V6zm0 4h8v2H2v-2z"/>
    </svg>
  ),
  'application-examples': (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M1 1h14v14H1V1zm2 2v10h10V3H3z"/>
    </svg>
  ),
  'application-components': (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M3 3h4v4H3V3zm6 0h4v4H9V3zM3 9h4v4H3V9zm6 0h4v4H9V9z"/>
    </svg>
  ),
};

export default function Sidebar({ categories = designSystemCategories, currentPath }: SidebarProps) {
  const pathname = usePathname();
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['foundations']));

  const toggleCategory = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  const isActive = (href: string) => {
    return pathname === href;
  };

  const isCategoryActive = (categoryId: string) => {
    return pathname.startsWith(`/${categoryId}`);
  };

  return (
    <nav className="fixed left-0 top-0 z-10 h-screen w-[280px] overflow-y-auto border-r bg-gray-50">
      {/* Header */}
      <div className="border-b p-6">
        <Link href="/">
          <h1 className="text-xl font-bold text-gray-900">
            Design System
          </h1>
        </Link>
        <p className="mt-1 text-sm text-gray-500">
          Chakra UI RSC Library
        </p>
        <div className="mt-3">
          <Link href="/figma-sync">
            <div className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700">
              <svg className="w-4 h-4" viewBox="0 0 16 16" fill="currentColor">
                <path d="M8 1a7 7 0 100 14A7 7 0 008 1zM6 6a1 1 0 112 0 1 1 0 01-2 0zm1 3a1 1 0 100 2 1 1 0 000-2z"/>
              </svg>
              <span>Figma Sync</span>
            </div>
          </Link>
        </div>
      </div>

      {/* Navigation */}
      <div className="p-4">
        {categories.map((category) => {
          const isExpanded = expandedCategories.has(category.id);
          const isCatActive = isCategoryActive(category.id);

          return (
            <div key={category.id} className="mb-2">
              {/* Category Header */}
              <button
                onClick={() => toggleCategory(category.id)}
                className={`w-full flex items-center justify-between p-3 text-left text-sm font-medium rounded transition-colors ${
                  isCatActive
                    ? 'bg-gray-200 text-gray-900'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                }`}
              >
                <div className="flex items-center gap-3">
                  <span className="w-4 h-4">
                    {CategoryIcons[category.id as keyof typeof CategoryIcons]}
                  </span>
                  <span>{category.title}</span>
                </div>
                <span
                  className={`w-3 h-3 transition-transform ${
                    isExpanded ? 'rotate-90' : 'rotate-0'
                  }`}
                >
                  <svg viewBox="0 0 16 16" fill="currentColor">
                    <path d="M6 4l4 4-4 4V4z"/>
                  </svg>
                </span>
              </button>

              {/* Category Components */}
              {isExpanded && (
                <div className="ml-6 py-2">
                  {category.components.map((component) => {
                    const componentHref = `/${category.id}/${component.id}`;
                    const isComponentActive = isActive(componentHref);

                    return (
                      <Link key={component.id} href={componentHref}>
                        <div
                          className={`block p-2 text-sm rounded transition-colors ${
                            isComponentActive
                              ? 'bg-blue-50 text-blue-600'
                              : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                          }`}
                        >
                          {component.name}
                        </div>
                      </Link>
                    );
                  })}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </nav>
  );
}
