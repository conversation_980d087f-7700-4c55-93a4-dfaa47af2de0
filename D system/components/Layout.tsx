'use client';

import Sidebar from './Sidebar';

interface LayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  return (
    <div className="min-h-screen bg-white">
      {/* Sidebar */}
      <Sidebar />

      {/* Main Content */}
      <div className="ml-[280px] min-h-screen bg-white">
        <div className="container py-8 px-8">
          {children}
        </div>
      </div>
    </div>
  );
}
