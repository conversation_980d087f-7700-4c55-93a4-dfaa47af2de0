[{"id": "resources=false,-type=published-components", "name": "Resources=False, Type=Published components", "description": "Resources=False, Type=Published components component from Figma", "figmaId": "1532:291632", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1532:291632", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "_swatch-base", "name": "_Swatch base", "description": "_Swatch base component from Figma", "figmaId": "1029:37647", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1029:37647", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "_swatch-base-gradient", "name": "_Swatch base gradient", "description": "_Swatch base gradient component from Figma", "figmaId": "1272:4548", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1272:4548", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "resources=true,-type=base-components", "name": "Resources=True, Type=Base components", "description": "Resources=True, Type=Base components component from Figma", "figmaId": "1518:346066", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1518:346066", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "_type-scale-base", "name": "_Type scale base", "description": "_Type scale base component from Figma", "figmaId": "1019:35537", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1019:35537", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "logomark", "name": "Logomark", "description": "Logomark component from Figma", "figmaId": "1083:50505", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1083:50505", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "resources=true,-type=published-components", "name": "Resources=True, Type=Published components", "description": "Resources=True, Type=Published components component from Figma", "figmaId": "1518:345435", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1518:345435", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "size=md", "name": "Size=md", "description": "Size=md component from Figma", "figmaId": "2680:401975", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=2680:401975", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "_key", "name": "_Key", "description": "_Key component from Figma", "figmaId": "1101:104589", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1101:104589", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "design-system-footer", "name": "Design system footer", "description": "Design system footer component from Figma", "figmaId": "1023:37095", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1023:37095", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "icon=false", "name": "Icon=False", "description": "Icon=False component from Figma", "figmaId": "1046:10170", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1046:10170", "category": "shared-assets", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "icon=leading", "name": "Icon=Leading", "description": "Icon=Leading component from Figma", "figmaId": "1046:10172", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1046:10172", "category": "shared-assets", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "_safari-footer", "name": "_Safari footer", "description": "_Safari footer component from Figma", "figmaId": "1868:671028", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1868:671028", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "_safari-address-bar", "name": "_Safari address bar", "description": "_Safari address bar component from Figma", "figmaId": "1868:671036", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1868:671036", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "resources=false,-type=examples", "name": "Resources=False, Type=Examples", "description": "Resources=False, Type=Examples component from Figma", "figmaId": "1547:266257", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1547:266257", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "type=screen-mockup-05,-theme=default,-breakpoint=desktop", "name": "Type=Screen mockup 05, Theme=Default, Breakpoint=Desktop", "description": "Type=Screen mockup 05, Theme=Default, Breakpoint=Desktop component from Figma", "figmaId": "1316:3497", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1316:3497", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "type=user-invite,-breakpoint=desktop", "name": "Type=User invite, Breakpoint=Desktop", "description": "Type=User invite, Breakpoint=Desktop component from Figma", "figmaId": "4057:415518", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=4057:415518", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "type=4-col-with-footer,-desktop=desktop", "name": "Type=4-col with footer, Desktop=Desktop", "description": "Type=4-col with footer, Desktop=Desktop component from Figma", "figmaId": "1282:12816", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1282:12816", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "_google-maps-mockup", "name": "_Google maps mockup", "description": "_Google maps mockup component from Figma", "figmaId": "1438:225984", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1438:225984", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "_vector-map", "name": "_Vector map", "description": "_Vector map component from Figma", "figmaId": "1438:253884", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1438:253884", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "_command-bar-footer", "name": "_Command bar footer", "description": "_Command bar footer component from Figma", "figmaId": "4898:411379", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=4898:411379", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "_x-axis-label", "name": "_X-axis label", "description": "_X-axis label component from Figma", "figmaId": "1061:47348", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1061:47348", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "_y-axis-label", "name": "_Y-axis label", "description": "_Y-axis label component from Figma", "figmaId": "1061:47350", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1061:47350", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "_radar-chart", "name": "_Radar chart", "description": "_Radar chart component from Figma", "figmaId": "1084:7152", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1084:7152", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "type=notifications,-breakpoint=desktop", "name": "Type=Notifications, Breakpoint=Desktop", "description": "Type=Notifications, Breakpoint=Desktop component from Figma", "figmaId": "1251:2998", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1251:2998", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}, {"id": "type=message-chat,-breakpoint=desktop", "name": "Type=Message chat, Breakpoint=Desktop", "description": "Type=Message chat, Breakpoint=Desktop component from Figma", "figmaId": "1247:167", "figmaUrl": "https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1247:167", "category": "shared-components", "variants": [{"name": "<PERSON><PERSON><PERSON>", "props": {}}]}]