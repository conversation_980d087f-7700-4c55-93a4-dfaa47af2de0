"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/docs/foundations/colors/page",{

/***/ "(app-pages-browser)/./app/docs/foundations/colors/page.tsx":
/*!**********************************************!*\
  !*** ./app/docs/foundations/colors/page.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ColorsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_DocsLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/DocsLayout */ \"(app-pages-browser)/./components/DocsLayout.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ColorsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DocsLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"prose prose-lg max-w-none\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"not-prose mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-gray-900\",\n                                    children: \"Colors\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                    lineNumber: 13,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium\",\n                                    children: \"✨ NEW ✨\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 leading-relaxed\",\n                            children: \"Essential design tokens and foundational elements for colors. These form the building blocks of our design system.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                    children: \"Overview\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"Colors are fundamental design tokens that ensure consistency across your application. They provide the visual foundation that all other components build upon.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                    children: \"Usage\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"not-prose bg-gray-900 rounded-lg p-4 my-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"text-green-400 text-sm overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            children: 'import { Colors } from \\'@your-org/design-system\\';\\n\\nexport function Example() {\\n  return (\\n    <Colors\\n      variant=\"primary\"\\n      size=\"md\"\\n    >\\n      Colors Example\\n    </Colors>\\n  );\\n}'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                    children: \"Examples\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"not-prose grid grid-cols-1 md:grid-cols-2 gap-6 my-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-gray-900 mb-3\",\n                                    children: \"Default Colors\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-lg p-4 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-sm\",\n                                        children: \"Preview of colors\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-gray-900 mb-3\",\n                                    children: \"Variant Example\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-lg p-4 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-sm\",\n                                        children: \"Alternative colors style\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                    children: \"Props & Variants\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"not-prose overflow-x-auto mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full border border-gray-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-4 py-2 text-left text-sm font-medium text-gray-900\",\n                                            children: \"Prop\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-4 py-2 text-left text-sm font-medium text-gray-900\",\n                                            children: \"Type\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-4 py-2 text-left text-sm font-medium text-gray-900\",\n                                            children: \"Default\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-4 py-2 text-left text-sm font-medium text-gray-900\",\n                                            children: \"Description\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                children: \"variant\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"string\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: '\"default\"'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"Visual style variant\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                children: \"size\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"string\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: '\"md\"'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"Component size\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                children: \"disabled\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"boolean\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"false\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"Disable interaction\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                children: \"className\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"string\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"Additional CSS classes\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                    children: \"Best Practices\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"list-disc list-inside text-gray-600 space-y-2 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Use colors consistently across your application\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Follow accessibility guidelines when implementing\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Test components across different screen sizes\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Maintain proper contrast ratios\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Use semantic HTML elements when possible\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                    children: \"Related Components\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"not-prose bg-gray-50 rounded-lg p-6 mt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Related Components\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/docs/foundations/colors\",\n                                    children: \"Colors\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/docs/foundations/typography\",\n                                    children: \"Typography\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n_c = ColorsPage;\nvar _c;\n$RefreshReg$(_c, \"ColorsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/docs/foundations/colors/page.tsx\n"));

/***/ })

});