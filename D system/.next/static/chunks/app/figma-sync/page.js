/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/figma-sync/page"],{

/***/ "(app-pages-browser)/./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Sidebar */ \"(app-pages-browser)/./components/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Layout(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-[280px] min-h-screen bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container py-8 px-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_c = Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvTGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRWdDO0FBTWpCLFNBQVNDLE9BQU8sS0FBeUI7UUFBekIsRUFBRUMsUUFBUSxFQUFlLEdBQXpCO0lBQzdCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0osZ0RBQU9BOzs7OzswQkFHUiw4REFBQ0c7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNaRjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWDtLQWR3QkQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYWhtb3VkbWV0d2FseS9Eb2N1bWVudHMvUHJvamVjdHMvRCBzeXN0ZW0vY29tcG9uZW50cy9MYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFNpZGViYXIgZnJvbSAnLi9TaWRlYmFyJztcblxuaW50ZXJmYWNlIExheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGF5b3V0KHsgY2hpbGRyZW4gfTogTGF5b3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy13aGl0ZVwiPlxuICAgICAgey8qIFNpZGViYXIgKi99XG4gICAgICA8U2lkZWJhciAvPlxuXG4gICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC1bMjgwcHhdIG1pbi1oLXNjcmVlbiBiZy13aGl0ZVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBweS04IHB4LThcIj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiU2lkZWJhciIsIkxheW91dCIsImNoaWxkcmVuIiwiZGl2IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Layout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/Sidebar.tsx":
/*!********************************!*\
  !*** ./components/Sidebar.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_design_system__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/design-system */ \"(app-pages-browser)/./lib/design-system.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Icons for categories (using simple SVG icons)\nconst CategoryIcons = {\n    thumbnail: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M2 3a1 1 0 011-1h10a1 1 0 011 1v10a1 1 0 01-1 1H3a1 1 0 01-1-1V3zm2 1v8h8V4H4z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined),\n    foundations: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M8 1l7 4v6l-7 4-7-4V5l7-4zm0 2L3.5 5.5 8 8l4.5-2.5L8 3zm-5 4v4l5 2.5V9.5L3 7zm10 0L8 9.5V14l5-2.5V7z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined),\n    'shared-components': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M4 2a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h8v8H4V4z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined),\n    'shared-assets': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M8 1a7 7 0 100 14A7 7 0 008 1zM6 6a1 1 0 112 0 1 1 0 01-2 0zm1 3a1 1 0 100 2 1 1 0 000-2z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined),\n    'marketing-website-examples': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M1 2a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H2a1 1 0 01-1-1V2zm2 1v10h10V3H3z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined),\n    'marketing-website-components': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M2 2h12v2H2V2zm0 4h12v2H2V6zm0 4h8v2H2v-2z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined),\n    'application-examples': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M1 1h14v14H1V1zm2 2v10h10V3H3z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined),\n    'application-components': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M3 3h4v4H3V3zm6 0h4v4H9V3zM3 9h4v4H3V9zm6 0h4v4H9V9z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined)\n};\nfunction Sidebar(param) {\n    let { categories = _lib_design_system__WEBPACK_IMPORTED_MODULE_4__.designSystemCategories, currentPath } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'foundations'\n    ]));\n    const toggleCategory = (categoryId)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(categoryId)) {\n            newExpanded.delete(categoryId);\n        } else {\n            newExpanded.add(categoryId);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const isActive = (href)=>{\n        return pathname === href;\n    };\n    const isCategoryActive = (categoryId)=>{\n        return pathname.startsWith(\"/\".concat(categoryId));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed left-0 top-0 z-10 h-screen w-[280px] overflow-y-auto border-r bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"Design System\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-gray-500\",\n                        children: \"Chakra UI RSC Library\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/figma-sync\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        viewBox: \"0 0 16 16\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M8 1a7 7 0 100 14A7 7 0 008 1zM6 6a1 1 0 112 0 1 1 0 01-2 0zm1 3a1 1 0 100 2 1 1 0 000-2z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Figma Sync\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: categories.map((category)=>{\n                    const isExpanded = expandedCategories.has(category.id);\n                    const isCatActive = isCategoryActive(category.id);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>toggleCategory(category.id),\n                                className: \"w-full flex items-center justify-between p-3 text-left text-sm font-medium rounded transition-colors \".concat(isCatActive ? 'bg-gray-200 text-gray-900' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-4 h-4\",\n                                                children: CategoryIcons[category.id]\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: category.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"w-3 h-3 transition-transform \".concat(isExpanded ? 'rotate-90' : 'rotate-0'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            viewBox: \"0 0 16 16\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M6 4l4 4-4 4V4z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 15\n                            }, this),\n                            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-6 py-2\",\n                                children: category.components.map((component)=>{\n                                    const componentHref = \"/\".concat(category.id, \"/\").concat(component.id);\n                                    const isComponentActive = isActive(componentHref);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: componentHref,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"block p-2 text-sm rounded transition-colors \".concat(isComponentActive ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'),\n                                            children: component.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, component.id, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 23\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, category.id, true, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"vIIOCftGFcezquG9nJ1x87c3LdM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Sidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/design-system.ts":
/*!******************************!*\
  !*** ./lib/design-system.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   designSystemCategories: () => (/* binding */ designSystemCategories),\n/* harmony export */   getAllComponents: () => (/* binding */ getAllComponents),\n/* harmony export */   getCategoryById: () => (/* binding */ getCategoryById),\n/* harmony export */   getComponentById: () => (/* binding */ getComponentById),\n/* harmony export */   getComponentsByCategory: () => (/* binding */ getComponentsByCategory),\n/* harmony export */   originalDesignSystemCategories: () => (/* binding */ originalDesignSystemCategories)\n/* harmony export */ });\n/* harmony import */ var _figma_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./figma-components */ \"(app-pages-browser)/./lib/figma-components.ts\");\n\n// Design System Configuration based on Figma file\n// Use Figma-enhanced categories that include real components from the design system\nconst designSystemCategories = _figma_components__WEBPACK_IMPORTED_MODULE_0__.figmaEnhancedCategories;\n// Original design system categories (kept for reference)\nconst originalDesignSystemCategories = [\n    {\n        id: 'thumbnail',\n        title: 'Thumbnail',\n        description: 'Overview and preview of the design system',\n        components: [\n            {\n                id: 'overview',\n                name: 'Design System Overview',\n                category: 'thumbnail',\n                description: 'Complete overview of the design system components and guidelines',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=2210-441096',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {}\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'foundations',\n        title: 'Foundations',\n        description: 'Core design tokens, colors, typography, and spacing',\n        components: [\n            {\n                id: 'colors',\n                name: 'Color System',\n                category: 'foundations',\n                description: 'Primary, secondary, and semantic color palettes with accessibility guidelines',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=colors',\n                variants: [\n                    {\n                        name: 'Primary Palette',\n                        props: {\n                            palette: 'primary'\n                        }\n                    },\n                    {\n                        name: 'Secondary Palette',\n                        props: {\n                            palette: 'secondary'\n                        }\n                    },\n                    {\n                        name: 'Semantic Colors',\n                        props: {\n                            palette: 'semantic'\n                        }\n                    },\n                    {\n                        name: 'Neutral Colors',\n                        props: {\n                            palette: 'neutral'\n                        }\n                    },\n                    {\n                        name: 'Brand Colors',\n                        props: {\n                            palette: 'brand'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'primary-50',\n                        value: '#eff6ff',\n                        type: 'color',\n                        description: 'Primary color lightest shade'\n                    },\n                    {\n                        name: 'primary-500',\n                        value: '#3b82f6',\n                        type: 'color',\n                        description: 'Primary color base'\n                    },\n                    {\n                        name: 'primary-900',\n                        value: '#1e3a8a',\n                        type: 'color',\n                        description: 'Primary color darkest shade'\n                    }\n                ]\n            },\n            {\n                id: 'typography',\n                name: 'Typography Scale',\n                category: 'foundations',\n                description: 'Font families, sizes, weights, and line heights for consistent text hierarchy',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=typography',\n                variants: [\n                    {\n                        name: 'Display Text',\n                        props: {\n                            category: 'display'\n                        }\n                    },\n                    {\n                        name: 'Headings',\n                        props: {\n                            category: 'headings'\n                        }\n                    },\n                    {\n                        name: 'Body Text',\n                        props: {\n                            category: 'body'\n                        }\n                    },\n                    {\n                        name: 'Labels & Captions',\n                        props: {\n                            category: 'labels'\n                        }\n                    },\n                    {\n                        name: 'Code & Monospace',\n                        props: {\n                            category: 'code'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'font-size-xs',\n                        value: '12px',\n                        type: 'typography',\n                        description: 'Extra small text size'\n                    },\n                    {\n                        name: 'font-size-base',\n                        value: '16px',\n                        type: 'typography',\n                        description: 'Base text size'\n                    },\n                    {\n                        name: 'font-size-2xl',\n                        value: '24px',\n                        type: 'typography',\n                        description: 'Large heading size'\n                    }\n                ]\n            },\n            {\n                id: 'spacing',\n                name: 'Spacing System',\n                category: 'foundations',\n                description: '8px grid system for consistent spacing and layout',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=spacing',\n                variants: [\n                    {\n                        name: 'Base Scale',\n                        props: {\n                            scale: 'base'\n                        }\n                    },\n                    {\n                        name: 'Component Spacing',\n                        props: {\n                            scale: 'component'\n                        }\n                    },\n                    {\n                        name: 'Layout Spacing',\n                        props: {\n                            scale: 'layout'\n                        }\n                    },\n                    {\n                        name: 'Responsive Spacing',\n                        props: {\n                            scale: 'responsive'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'space-1',\n                        value: '4px',\n                        type: 'spacing',\n                        description: 'Smallest spacing unit'\n                    },\n                    {\n                        name: 'space-4',\n                        value: '16px',\n                        type: 'spacing',\n                        description: 'Base spacing unit'\n                    },\n                    {\n                        name: 'space-8',\n                        value: '32px',\n                        type: 'spacing',\n                        description: 'Large spacing unit'\n                    }\n                ]\n            },\n            {\n                id: 'elevation',\n                name: 'Elevation & Shadows',\n                category: 'foundations',\n                description: 'Shadow system for creating depth and visual hierarchy',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=elevation',\n                variants: [\n                    {\n                        name: 'Card Shadows',\n                        props: {\n                            type: 'card'\n                        }\n                    },\n                    {\n                        name: 'Modal Shadows',\n                        props: {\n                            type: 'modal'\n                        }\n                    },\n                    {\n                        name: 'Focus Rings',\n                        props: {\n                            type: 'focus'\n                        }\n                    },\n                    {\n                        name: 'Dropdown Shadows',\n                        props: {\n                            type: 'dropdown'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'shadow-sm',\n                        value: '0 1px 2px 0 rgb(0 0 0 / 0.05)',\n                        type: 'shadow',\n                        description: 'Small shadow'\n                    },\n                    {\n                        name: 'shadow-md',\n                        value: '0 4px 6px -1px rgb(0 0 0 / 0.1)',\n                        type: 'shadow',\n                        description: 'Medium shadow'\n                    },\n                    {\n                        name: 'shadow-lg',\n                        value: '0 10px 15px -3px rgb(0 0 0 / 0.1)',\n                        type: 'shadow',\n                        description: 'Large shadow'\n                    }\n                ]\n            },\n            {\n                id: 'grid',\n                name: 'Grid System',\n                category: 'foundations',\n                description: 'Responsive grid system and layout guidelines',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=grid',\n                variants: [\n                    {\n                        name: '12 Column Grid',\n                        props: {\n                            columns: 12\n                        }\n                    },\n                    {\n                        name: 'Responsive Breakpoints',\n                        props: {\n                            type: 'breakpoints'\n                        }\n                    },\n                    {\n                        name: 'Container Sizes',\n                        props: {\n                            type: 'containers'\n                        }\n                    },\n                    {\n                        name: 'Gutters & Margins',\n                        props: {\n                            type: 'gutters'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'shared-components',\n        title: 'Shared Components',\n        description: 'Reusable UI components used across applications',\n        components: [\n            {\n                id: 'button',\n                name: 'Button',\n                category: 'shared-components',\n                description: 'Primary interactive element with multiple variants and states',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=button',\n                variants: [\n                    {\n                        name: 'Primary',\n                        props: {\n                            variant: 'primary',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Secondary',\n                        props: {\n                            variant: 'secondary',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Outline',\n                        props: {\n                            variant: 'outline',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Ghost',\n                        props: {\n                            variant: 'ghost',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Destructive',\n                        props: {\n                            variant: 'destructive',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Small',\n                        props: {\n                            variant: 'primary',\n                            size: 'sm'\n                        }\n                    },\n                    {\n                        name: 'Large',\n                        props: {\n                            variant: 'primary',\n                            size: 'lg'\n                        }\n                    },\n                    {\n                        name: 'Icon Only',\n                        props: {\n                            variant: 'primary',\n                            iconOnly: true\n                        }\n                    },\n                    {\n                        name: 'Loading',\n                        props: {\n                            variant: 'primary',\n                            loading: true\n                        }\n                    },\n                    {\n                        name: 'Disabled',\n                        props: {\n                            variant: 'primary',\n                            disabled: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'input',\n                name: 'Input Field',\n                category: 'shared-components',\n                description: 'Text input with labels, validation, and helper text',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=input',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {\n                            placeholder: 'Enter text...'\n                        }\n                    },\n                    {\n                        name: 'With Label',\n                        props: {\n                            label: 'Email Address',\n                            placeholder: 'Enter your email'\n                        }\n                    },\n                    {\n                        name: 'Required',\n                        props: {\n                            label: 'Password',\n                            required: true,\n                            type: 'password'\n                        }\n                    },\n                    {\n                        name: 'Error State',\n                        props: {\n                            label: 'Username',\n                            error: 'Username is already taken'\n                        }\n                    },\n                    {\n                        name: 'Success State',\n                        props: {\n                            label: 'Email',\n                            success: 'Email is available'\n                        }\n                    },\n                    {\n                        name: 'Disabled',\n                        props: {\n                            label: 'Disabled Field',\n                            disabled: true\n                        }\n                    },\n                    {\n                        name: 'With Icon',\n                        props: {\n                            label: 'Search',\n                            icon: 'search',\n                            placeholder: 'Search...'\n                        }\n                    },\n                    {\n                        name: 'Textarea',\n                        props: {\n                            label: 'Message',\n                            type: 'textarea',\n                            rows: 4\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'card',\n                name: 'Card',\n                category: 'shared-components',\n                description: 'Flexible container for grouping related content',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=card',\n                variants: [\n                    {\n                        name: 'Basic Card',\n                        props: {\n                            variant: 'default'\n                        }\n                    },\n                    {\n                        name: 'Elevated Card',\n                        props: {\n                            variant: 'elevated'\n                        }\n                    },\n                    {\n                        name: 'Outlined Card',\n                        props: {\n                            variant: 'outlined'\n                        }\n                    },\n                    {\n                        name: 'Interactive Card',\n                        props: {\n                            variant: 'interactive',\n                            clickable: true\n                        }\n                    },\n                    {\n                        name: 'Product Card',\n                        props: {\n                            variant: 'product',\n                            image: true,\n                            badge: true\n                        }\n                    },\n                    {\n                        name: 'Profile Card',\n                        props: {\n                            variant: 'profile',\n                            avatar: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'badge',\n                name: 'Badge',\n                category: 'shared-components',\n                description: 'Small status indicators and labels',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=badge',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {\n                            variant: 'default'\n                        }\n                    },\n                    {\n                        name: 'Primary',\n                        props: {\n                            variant: 'primary'\n                        }\n                    },\n                    {\n                        name: 'Success',\n                        props: {\n                            variant: 'success'\n                        }\n                    },\n                    {\n                        name: 'Warning',\n                        props: {\n                            variant: 'warning'\n                        }\n                    },\n                    {\n                        name: 'Error',\n                        props: {\n                            variant: 'error'\n                        }\n                    },\n                    {\n                        name: 'Outline',\n                        props: {\n                            variant: 'outline'\n                        }\n                    },\n                    {\n                        name: 'Dot Indicator',\n                        props: {\n                            variant: 'dot'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'avatar',\n                name: 'Avatar',\n                category: 'shared-components',\n                description: 'User profile pictures and initials',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=avatar',\n                variants: [\n                    {\n                        name: 'Image Avatar',\n                        props: {\n                            type: 'image',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Initials',\n                        props: {\n                            type: 'initials',\n                            initials: 'JD',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Icon Avatar',\n                        props: {\n                            type: 'icon',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Small',\n                        props: {\n                            type: 'image',\n                            size: 'sm'\n                        }\n                    },\n                    {\n                        name: 'Large',\n                        props: {\n                            type: 'image',\n                            size: 'lg'\n                        }\n                    },\n                    {\n                        name: 'With Status',\n                        props: {\n                            type: 'image',\n                            status: 'online',\n                            size: 'md'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'dropdown',\n                name: 'Dropdown Menu',\n                category: 'shared-components',\n                description: 'Contextual menus and select components',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=dropdown',\n                variants: [\n                    {\n                        name: 'Basic Dropdown',\n                        props: {\n                            variant: 'basic'\n                        }\n                    },\n                    {\n                        name: 'With Icons',\n                        props: {\n                            variant: 'icons'\n                        }\n                    },\n                    {\n                        name: 'With Dividers',\n                        props: {\n                            variant: 'dividers'\n                        }\n                    },\n                    {\n                        name: 'Multi-select',\n                        props: {\n                            variant: 'multiselect'\n                        }\n                    },\n                    {\n                        name: 'Searchable',\n                        props: {\n                            variant: 'searchable'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'shared-assets',\n        title: 'Shared Assets',\n        description: 'Icons, illustrations, and other visual assets',\n        components: [\n            {\n                id: 'icons',\n                name: 'Icons',\n                category: 'shared-assets',\n                description: 'Icon library and usage guidelines',\n                variants: [\n                    {\n                        name: 'Interface',\n                        props: {\n                            category: 'interface'\n                        }\n                    },\n                    {\n                        name: 'Actions',\n                        props: {\n                            category: 'actions'\n                        }\n                    },\n                    {\n                        name: 'Status',\n                        props: {\n                            category: 'status'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'illustrations',\n                name: 'Illustrations',\n                category: 'shared-assets',\n                description: 'Illustration library for empty states and onboarding',\n                variants: [\n                    {\n                        name: 'Empty States',\n                        props: {\n                            type: 'empty'\n                        }\n                    },\n                    {\n                        name: 'Onboarding',\n                        props: {\n                            type: 'onboarding'\n                        }\n                    },\n                    {\n                        name: 'Error States',\n                        props: {\n                            type: 'error'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'marketing-website-examples',\n        title: 'Marketing Website Examples',\n        description: 'Complete page examples for marketing websites',\n        components: [\n            {\n                id: 'landing-page',\n                name: 'Landing Page',\n                category: 'marketing-website-examples',\n                description: 'Complete landing page layout with hero, features, and CTA',\n                variants: [\n                    {\n                        name: 'SaaS',\n                        props: {\n                            type: 'saas'\n                        }\n                    },\n                    {\n                        name: 'Product',\n                        props: {\n                            type: 'product'\n                        }\n                    },\n                    {\n                        name: 'Agency',\n                        props: {\n                            type: 'agency'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'pricing-page',\n                name: 'Pricing Page',\n                category: 'marketing-website-examples',\n                description: 'Pricing page with plans and feature comparison',\n                variants: [\n                    {\n                        name: 'Simple',\n                        props: {\n                            layout: 'simple'\n                        }\n                    },\n                    {\n                        name: 'Comparison',\n                        props: {\n                            layout: 'comparison'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'marketing-website-components',\n        title: 'Marketing Website Components',\n        description: 'Specialized components for marketing websites',\n        components: [\n            {\n                id: 'hero-section',\n                name: 'Hero Section',\n                category: 'marketing-website-components',\n                description: 'Hero section with headline, description, and CTA',\n                variants: [\n                    {\n                        name: 'Centered',\n                        props: {\n                            alignment: 'center'\n                        }\n                    },\n                    {\n                        name: 'Left Aligned',\n                        props: {\n                            alignment: 'left'\n                        }\n                    },\n                    {\n                        name: 'With Image',\n                        props: {\n                            hasImage: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'feature-grid',\n                name: 'Feature Grid',\n                category: 'marketing-website-components',\n                description: 'Grid layout for showcasing product features',\n                variants: [\n                    {\n                        name: '3 Column',\n                        props: {\n                            columns: 3\n                        }\n                    },\n                    {\n                        name: '4 Column',\n                        props: {\n                            columns: 4\n                        }\n                    },\n                    {\n                        name: 'With Icons',\n                        props: {\n                            hasIcons: true\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'application-examples',\n        title: 'Application Examples',\n        description: 'Complete application page layouts and flows',\n        components: [\n            {\n                id: 'dashboard',\n                name: 'Dashboard',\n                category: 'application-examples',\n                description: 'Complete dashboard layout with sidebar and content area',\n                variants: [\n                    {\n                        name: 'Analytics',\n                        props: {\n                            type: 'analytics'\n                        }\n                    },\n                    {\n                        name: 'CRM',\n                        props: {\n                            type: 'crm'\n                        }\n                    },\n                    {\n                        name: 'E-commerce',\n                        props: {\n                            type: 'ecommerce'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'settings-page',\n                name: 'Settings Page',\n                category: 'application-examples',\n                description: 'Settings page with tabs and form sections',\n                variants: [\n                    {\n                        name: 'Profile',\n                        props: {\n                            section: 'profile'\n                        }\n                    },\n                    {\n                        name: 'Security',\n                        props: {\n                            section: 'security'\n                        }\n                    },\n                    {\n                        name: 'Billing',\n                        props: {\n                            section: 'billing'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'application-components',\n        title: 'Application Components',\n        description: 'Complex components for application interfaces',\n        components: [\n            {\n                id: 'data-table',\n                name: 'Data Table',\n                category: 'application-components',\n                description: 'Advanced data table with sorting, filtering, and pagination',\n                variants: [\n                    {\n                        name: 'Basic',\n                        props: {\n                            features: [\n                                'sorting'\n                            ]\n                        }\n                    },\n                    {\n                        name: 'Advanced',\n                        props: {\n                            features: [\n                                'sorting',\n                                'filtering',\n                                'pagination'\n                            ]\n                        }\n                    },\n                    {\n                        name: 'Selectable',\n                        props: {\n                            selectable: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'sidebar-navigation',\n                name: 'Sidebar Navigation',\n                category: 'application-components',\n                description: 'Collapsible sidebar navigation for applications',\n                variants: [\n                    {\n                        name: 'Expanded',\n                        props: {\n                            collapsed: false\n                        }\n                    },\n                    {\n                        name: 'Collapsed',\n                        props: {\n                            collapsed: true\n                        }\n                    },\n                    {\n                        name: 'With Submenu',\n                        props: {\n                            hasSubmenu: true\n                        }\n                    }\n                ]\n            }\n        ]\n    }\n];\n// Utility functions\nfunction getCategoryById(id) {\n    return designSystemCategories.find((category)=>category.id === id);\n}\nfunction getComponentById(componentId) {\n    for (const category of designSystemCategories){\n        const component = category.components.find((comp)=>comp.id === componentId);\n        if (component) return component;\n    }\n    return undefined;\n}\nfunction getAllComponents() {\n    return designSystemCategories.flatMap((category)=>category.components);\n}\nfunction getComponentsByCategory(categoryId) {\n    const category = getCategoryById(categoryId);\n    return (category === null || category === void 0 ? void 0 : category.components) || [];\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/design-system.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/figma-components.ts":
/*!*********************************!*\
  !*** ./lib/figma-components.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   figmaComponents: () => (/* binding */ figmaComponents),\n/* harmony export */   figmaEnhancedCategories: () => (/* binding */ figmaEnhancedCategories),\n/* harmony export */   getFigmaComponentById: () => (/* binding */ getFigmaComponentById),\n/* harmony export */   getFigmaComponentsByCategory: () => (/* binding */ getFigmaComponentsByCategory)\n/* harmony export */ });\n// Real Figma components extracted from the design system file\n// Load the synced components from Figma\nconst figmaComponents = [\n    {\n        id: 'logomark',\n        name: 'Logomark',\n        category: 'foundations',\n        description: 'Brand logomark component from Figma design system',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1083:50505',\n        figmaId: '1083:50505',\n        variants: [\n            {\n                name: 'Default',\n                props: {}\n            },\n            {\n                name: 'Small',\n                props: {\n                    size: 'sm'\n                }\n            },\n            {\n                name: 'Large',\n                props: {\n                    size: 'lg'\n                }\n            }\n        ]\n    },\n    {\n        id: 'swatch-base',\n        name: 'Color Swatch',\n        category: 'foundations',\n        description: 'Color swatch component for displaying color tokens',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1029:37647',\n        figmaId: '1029:37647',\n        variants: [\n            {\n                name: 'Solid',\n                props: {\n                    type: 'solid'\n                }\n            },\n            {\n                name: 'Gradient',\n                props: {\n                    type: 'gradient'\n                }\n            }\n        ]\n    },\n    {\n        id: 'type-scale-base',\n        name: 'Typography Scale',\n        category: 'foundations',\n        description: 'Typography scale component showing font sizes and weights',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1019:35537',\n        figmaId: '1019:35537',\n        variants: [\n            {\n                name: 'Display',\n                props: {\n                    category: 'display'\n                }\n            },\n            {\n                name: 'Headings',\n                props: {\n                    category: 'headings'\n                }\n            },\n            {\n                name: 'Body',\n                props: {\n                    category: 'body'\n                }\n            },\n            {\n                name: 'Small',\n                props: {\n                    category: 'small'\n                }\n            }\n        ]\n    },\n    {\n        id: 'button-icon',\n        name: 'Button with Icon',\n        category: 'shared-components',\n        description: 'Button component with icon variants from Figma',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1046:10170',\n        figmaId: '1046:10170',\n        variants: [\n            {\n                name: 'No Icon',\n                props: {\n                    icon: false\n                }\n            },\n            {\n                name: 'Leading Icon',\n                props: {\n                    icon: 'leading'\n                }\n            },\n            {\n                name: 'Trailing Icon',\n                props: {\n                    icon: 'trailing'\n                }\n            }\n        ]\n    },\n    {\n        id: 'size-variants',\n        name: 'Size Variants',\n        category: 'shared-components',\n        description: 'Component size variants (sm, md, lg)',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=2680:401975',\n        figmaId: '2680:401975',\n        variants: [\n            {\n                name: 'Small',\n                props: {\n                    size: 'sm'\n                }\n            },\n            {\n                name: 'Medium',\n                props: {\n                    size: 'md'\n                }\n            },\n            {\n                name: 'Large',\n                props: {\n                    size: 'lg'\n                }\n            }\n        ]\n    },\n    {\n        id: 'design-system-footer',\n        name: 'Design System Footer',\n        category: 'shared-components',\n        description: 'Footer component for design system documentation',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1023:37095',\n        figmaId: '1023:37095',\n        variants: [\n            {\n                name: 'Default',\n                props: {}\n            },\n            {\n                name: 'Compact',\n                props: {\n                    variant: 'compact'\n                }\n            }\n        ]\n    },\n    {\n        id: 'safari-mockup',\n        name: 'Safari Browser Mockup',\n        category: 'shared-assets',\n        description: 'Safari browser mockup for showcasing web applications',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1868:671028',\n        figmaId: '1868:671028',\n        variants: [\n            {\n                name: 'With Address Bar',\n                props: {\n                    addressBar: true\n                }\n            },\n            {\n                name: 'Clean',\n                props: {\n                    addressBar: false\n                }\n            }\n        ]\n    },\n    {\n        id: 'screen-mockup',\n        name: 'Screen Mockup',\n        category: 'shared-assets',\n        description: 'Device screen mockup for presentations',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1316:3497',\n        figmaId: '1316:3497',\n        variants: [\n            {\n                name: 'Desktop',\n                props: {\n                    breakpoint: 'desktop'\n                }\n            },\n            {\n                name: 'Tablet',\n                props: {\n                    breakpoint: 'tablet'\n                }\n            },\n            {\n                name: 'Mobile',\n                props: {\n                    breakpoint: 'mobile'\n                }\n            }\n        ]\n    },\n    {\n        id: 'email-template',\n        name: 'Email Template',\n        category: 'shared-assets',\n        description: 'Email template for user invitations and notifications',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=4057:415518',\n        figmaId: '4057:415518',\n        variants: [\n            {\n                name: 'User Invite',\n                props: {\n                    type: 'user-invite'\n                }\n            },\n            {\n                name: 'Welcome',\n                props: {\n                    type: 'welcome'\n                }\n            },\n            {\n                name: 'Notification',\n                props: {\n                    type: 'notification'\n                }\n            }\n        ]\n    },\n    {\n        id: 'footer-layout',\n        name: 'Footer Layout',\n        category: 'marketing-website-components',\n        description: 'Website footer with multiple column layouts',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1282:12816',\n        figmaId: '1282:12816',\n        variants: [\n            {\n                name: '4 Column',\n                props: {\n                    columns: 4\n                }\n            },\n            {\n                name: '3 Column',\n                props: {\n                    columns: 3\n                }\n            },\n            {\n                name: '2 Column',\n                props: {\n                    columns: 2\n                }\n            }\n        ]\n    },\n    {\n        id: 'maps-integration',\n        name: 'Maps Integration',\n        category: 'application-components',\n        description: 'Google Maps and vector map components',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1438:225984',\n        figmaId: '1438:225984',\n        variants: [\n            {\n                name: 'Google Maps',\n                props: {\n                    type: 'google-maps'\n                }\n            },\n            {\n                name: 'Vector Map',\n                props: {\n                    type: 'vector'\n                }\n            }\n        ]\n    },\n    {\n        id: 'command-bar',\n        name: 'Command Bar',\n        category: 'application-components',\n        description: 'Command palette for quick actions and navigation',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=4898:411379',\n        figmaId: '4898:411379',\n        variants: [\n            {\n                name: 'Default',\n                props: {}\n            },\n            {\n                name: 'With Footer',\n                props: {\n                    footer: true\n                }\n            }\n        ]\n    },\n    {\n        id: 'charts',\n        name: 'Chart Components',\n        category: 'application-components',\n        description: 'Data visualization charts including radar charts',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1084:7152',\n        figmaId: '1084:7152',\n        variants: [\n            {\n                name: 'Radar Chart',\n                props: {\n                    type: 'radar'\n                }\n            },\n            {\n                name: 'Bar Chart',\n                props: {\n                    type: 'bar'\n                }\n            },\n            {\n                name: 'Line Chart',\n                props: {\n                    type: 'line'\n                }\n            }\n        ]\n    },\n    {\n        id: 'notifications',\n        name: 'Notification System',\n        category: 'application-components',\n        description: 'Notification components for desktop applications',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1251:2998',\n        figmaId: '1251:2998',\n        variants: [\n            {\n                name: 'Toast',\n                props: {\n                    type: 'toast'\n                }\n            },\n            {\n                name: 'Banner',\n                props: {\n                    type: 'banner'\n                }\n            },\n            {\n                name: 'Modal',\n                props: {\n                    type: 'modal'\n                }\n            }\n        ]\n    },\n    {\n        id: 'messaging',\n        name: 'Messaging Interface',\n        category: 'application-components',\n        description: 'Chat and messaging components for applications',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1247:167',\n        figmaId: '1247:167',\n        variants: [\n            {\n                name: 'Chat Bubble',\n                props: {\n                    type: 'bubble'\n                }\n            },\n            {\n                name: 'Message List',\n                props: {\n                    type: 'list'\n                }\n            },\n            {\n                name: 'Input Area',\n                props: {\n                    type: 'input'\n                }\n            }\n        ]\n    }\n];\n// Enhanced categories with real Figma components\nconst figmaEnhancedCategories = [\n    {\n        id: 'thumbnail',\n        title: 'Thumbnail',\n        description: 'Overview and preview of the design system',\n        components: [\n            {\n                id: 'overview',\n                name: 'Design System Overview',\n                category: 'thumbnail',\n                description: 'Complete overview of the design system components and guidelines',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=2210-441096',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {}\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'foundations',\n        title: 'Foundations',\n        description: 'Core design tokens, colors, typography, and spacing from Figma',\n        components: figmaComponents.filter((c)=>c.category === 'foundations')\n    },\n    {\n        id: 'shared-components',\n        title: 'Shared Components',\n        description: 'Reusable UI components synced from Figma design system',\n        components: figmaComponents.filter((c)=>c.category === 'shared-components')\n    },\n    {\n        id: 'shared-assets',\n        title: 'Shared Assets',\n        description: 'Icons, mockups, and visual assets from Figma',\n        components: figmaComponents.filter((c)=>c.category === 'shared-assets')\n    },\n    {\n        id: 'marketing-website-examples',\n        title: 'Marketing Website Examples',\n        description: 'Complete page examples for marketing websites',\n        components: [\n            {\n                id: 'landing-pages',\n                name: 'Landing Pages',\n                category: 'marketing-website-examples',\n                description: 'Complete landing page layouts from Figma',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=landing-pages',\n                variants: [\n                    {\n                        name: 'SaaS Landing',\n                        props: {\n                            type: 'saas'\n                        }\n                    },\n                    {\n                        name: 'Product Landing',\n                        props: {\n                            type: 'product'\n                        }\n                    },\n                    {\n                        name: 'Agency Landing',\n                        props: {\n                            type: 'agency'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'pricing-pages',\n                name: 'Pricing Pages',\n                category: 'marketing-website-examples',\n                description: 'Pricing page layouts with different structures',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=pricing-pages',\n                variants: [\n                    {\n                        name: 'Simple Pricing',\n                        props: {\n                            layout: 'simple'\n                        }\n                    },\n                    {\n                        name: 'Feature Comparison',\n                        props: {\n                            layout: 'comparison'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'marketing-website-components',\n        title: 'Marketing Website Components',\n        description: 'Marketing-specific components from Figma',\n        components: figmaComponents.filter((c)=>c.category === 'marketing-website-components')\n    },\n    {\n        id: 'application-examples',\n        title: 'Application Examples',\n        description: 'Complete application layouts from Figma',\n        components: [\n            {\n                id: 'dashboards',\n                name: 'Dashboard Examples',\n                category: 'application-examples',\n                description: 'Complete dashboard layouts from Figma',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=dashboards',\n                variants: [\n                    {\n                        name: 'Analytics Dashboard',\n                        props: {\n                            type: 'analytics'\n                        }\n                    },\n                    {\n                        name: 'CRM Dashboard',\n                        props: {\n                            type: 'crm'\n                        }\n                    },\n                    {\n                        name: 'E-commerce Dashboard',\n                        props: {\n                            type: 'ecommerce'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'application-components',\n        title: 'Application Components',\n        description: 'Complex application components synced from Figma',\n        components: figmaComponents.filter((c)=>c.category === 'application-components')\n    }\n];\n// Function to get Figma component by ID\nfunction getFigmaComponentById(id) {\n    return figmaComponents.find((component)=>component.id === id);\n}\n// Function to get all Figma components by category\nfunction getFigmaComponentsByCategory(category) {\n    return figmaComponents.filter((component)=>component.category === category);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/figma-components.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiL1VzZXJzL21haG1vdWRtZXR3YWx5L0RvY3VtZW50cy9Qcm9qZWN0cy9EIHN5c3RlbS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9uYXZpZ2F0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb24nO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1uYXZpZ2F0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Layout.tsx */ \"(app-pages-browser)/./components/Layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZtYWhtb3VkbWV0d2FseSUyRkRvY3VtZW50cyUyRlByb2plY3RzJTJGRCUyMHN5c3RlbSUyRmNvbXBvbmVudHMlMkZMYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUF5SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9tYWhtb3VkbWV0d2FseS9Eb2N1bWVudHMvUHJvamVjdHMvRCBzeXN0ZW0vY29tcG9uZW50cy9MYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/app-dir/link.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */ default: function() {\n        return LinkComponent;\n    },\n    useLinkStatus: function() {\n        return useLinkStatus;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _formaturl = __webpack_require__(/*! ../../shared/lib/router/utils/format-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ../components/router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _usemergedref = __webpack_require__(/*! ../use-merged-ref */ \"(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\");\nconst _utils = __webpack_require__(/*! ../../shared/lib/utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _warnonce = __webpack_require__(/*! ../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _links = __webpack_require__(/*! ../components/links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst _islocalurl = __webpack_require__(/*! ../../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _approuterinstance = __webpack_require__(/*! ../components/app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _erroronce = __webpack_require__(/*! ../../shared/lib/utils/error-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\");\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        if (replace) {\n            // browser default behavior does not replace the history state\n            // so we need to do it manually\n            e.preventDefault();\n            location.replace(href);\n        }\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        if (onNavigate) {\n            let isDefaultPrevented = false;\n            onNavigate({\n                preventDefault: ()=>{\n                    isDefaultPrevented = true;\n                }\n            });\n            if (isDefaultPrevented) {\n                return;\n            }\n        }\n        (0, _approuterinstance.dispatchNavigateAction)(as || href, replace ? 'replace' : 'push', scroll != null ? scroll : true, linkInstanceRef.current);\n    };\n    _react.default.startTransition(navigate);\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\nfunction LinkComponent(props) {\n    _s();\n    const [linkStatus, setOptimisticLinkStatus] = (0, _react.useOptimistic)(_links.IDLE_LINK_STATUS);\n    let children;\n    const linkInstanceRef = (0, _react.useRef)(null);\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, onNavigate, ref: forwardedRef, unstable_dynamicOnHover, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const router = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return Object.defineProperty(new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                value: \"E319\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            unstable_dynamicOnHover: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true,\n            onNavigate: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'prefetch' || key === 'legacyBehavior' || key === 'unstable_dynamicOnHover') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n    }\n    if (true) {\n        if (props.locale) {\n            (0, _warnonce.warnOnce)('The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization');\n        }\n        if (!asProp) {\n            let href;\n            if (typeof hrefProp === 'string') {\n                href = hrefProp;\n            } else if (typeof hrefProp === 'object' && typeof hrefProp.pathname === 'string') {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split('/').some((segment)=>segment.startsWith('[') && segment.endsWith(']'));\n                if (hasDynamicSegment) {\n                    throw Object.defineProperty(new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E267\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo({\n        \"LinkComponent.useMemo\": ()=>{\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n    }[\"LinkComponent.useMemo\"], [\n        hrefProp,\n        asProp\n    ]);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw Object.defineProperty(new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E320\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                throw Object.defineProperty(new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                    value: \"E266\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E209\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n    // mount. In the future we will also use this to keep track of all the\n    // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n    // a revalidation or refresh.\n    const observeLinkVisibilityOnMount = _react.default.useCallback({\n        \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": (element)=>{\n            if (router !== null) {\n                linkInstanceRef.current = (0, _links.mountLinkInstance)(element, href, router, appPrefetchKind, prefetchEnabled, setOptimisticLinkStatus);\n            }\n            return ({\n                \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": ()=>{\n                    if (linkInstanceRef.current) {\n                        (0, _links.unmountLinkForCurrentNavigation)(linkInstanceRef.current);\n                        linkInstanceRef.current = null;\n                    }\n                    (0, _links.unmountPrefetchableInstance)(element);\n                }\n            })[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"];\n        }\n    }[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"], [\n        prefetchEnabled,\n        href,\n        router,\n        appPrefetchKind,\n        setOptimisticLinkStatus\n    ]);\n    const mergedRef = (0, _usemergedref.useMergedRef)(observeLinkVisibilityOnMount, childRef);\n    const childProps = {\n        ref: mergedRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E312\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            if (!legacyBehavior && typeof onClick === 'function') {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled || \"development\" === 'development') {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled) {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        childProps.href = (0, _addbasepath.addBasePath)(as);\n    }\n    let link;\n    if (legacyBehavior) {\n        if (true) {\n            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\\n\\n' + 'npx @next/codemod@latest new-link .\\n\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');\n        }\n        link = /*#__PURE__*/ _react.default.cloneElement(child, childProps);\n    } else {\n        link = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            ...restProps,\n            ...childProps,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(LinkStatusContext.Provider, {\n        value: linkStatus,\n        children: link\n    });\n}\n_s(LinkComponent, \"MNV6IdWv8Lu3MKc7Fm4v59uGRY0=\");\n_c = LinkComponent;\nconst LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)(_links.IDLE_LINK_STATUS);\nconst useLinkStatus = ()=>{\n    return (0, _react.useContext)(LinkStatusContext);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c;\n$RefreshReg$(_c, \"LinkComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js":
/*!*********************************************************!*\
  !*** ./node_modules/next/dist/client/use-merged-ref.js ***!
  \*********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useMergedRef\", ({\n    enumerable: true,\n    get: function() {\n        return useMergedRef;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction useMergedRef(refA, refB) {\n    const cleanupA = (0, _react.useRef)(null);\n    const cleanupB = (0, _react.useRef)(null);\n    // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n    // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n    // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n    // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n    // (because it hasn't been updated for React 19)\n    // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n    // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n    return (0, _react.useCallback)((current)=>{\n        if (current === null) {\n            const cleanupFnA = cleanupA.current;\n            if (cleanupFnA) {\n                cleanupA.current = null;\n                cleanupFnA();\n            }\n            const cleanupFnB = cleanupB.current;\n            if (cleanupFnB) {\n                cleanupB.current = null;\n                cleanupFnB();\n            }\n        } else {\n            if (refA) {\n                cleanupA.current = applyRef(refA, current);\n            }\n            if (refB) {\n                cleanupB.current = applyRef(refB, current);\n            }\n        }\n    }, [\n        refA,\n        refB\n    ]);\n}\nfunction applyRef(refA, current) {\n    if (typeof refA === 'function') {\n        const cleanup = refA(current);\n        if (typeof cleanup === 'function') {\n            return cleanup;\n        } else {\n            return ()=>refA(null);\n        }\n    } else {\n        refA.current = current;\n        return ()=>{\n            refA.current = null;\n        };\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-merged-ref.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYWhtb3VkbWV0d2FseS9Eb2N1bWVudHMvUHJvamVjdHMvRCBzeXN0ZW0vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/format-url.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatUrl: function() {\n        return formatUrl;\n    },\n    formatWithValidation: function() {\n        return formatWithValidation;\n    },\n    urlObjectKeys: function() {\n        return urlObjectKeys;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _querystring = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./querystring */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\"));\nconst slashedProtocols = /https?|ftp|gopher|file/;\nfunction formatUrl(urlObj) {\n    let { auth, hostname } = urlObj;\n    let protocol = urlObj.protocol || '';\n    let pathname = urlObj.pathname || '';\n    let hash = urlObj.hash || '';\n    let query = urlObj.query || '';\n    let host = false;\n    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : '';\n    if (urlObj.host) {\n        host = auth + urlObj.host;\n    } else if (hostname) {\n        host = auth + (~hostname.indexOf(':') ? \"[\" + hostname + \"]\" : hostname);\n        if (urlObj.port) {\n            host += ':' + urlObj.port;\n        }\n    }\n    if (query && typeof query === 'object') {\n        query = String(_querystring.urlQueryToSearchParams(query));\n    }\n    let search = urlObj.search || query && \"?\" + query || '';\n    if (protocol && !protocol.endsWith(':')) protocol += ':';\n    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {\n        host = '//' + (host || '');\n        if (pathname && pathname[0] !== '/') pathname = '/' + pathname;\n    } else if (!host) {\n        host = '';\n    }\n    if (hash && hash[0] !== '#') hash = '#' + hash;\n    if (search && search[0] !== '?') search = '?' + search;\n    pathname = pathname.replace(/[?#]/g, encodeURIComponent);\n    search = search.replace('#', '%23');\n    return \"\" + protocol + host + pathname + search + hash;\n}\nconst urlObjectKeys = [\n    'auth',\n    'hash',\n    'host',\n    'hostname',\n    'href',\n    'path',\n    'pathname',\n    'port',\n    'protocol',\n    'query',\n    'search',\n    'slashes'\n];\nfunction formatWithValidation(url) {\n    if (true) {\n        if (url !== null && typeof url === 'object') {\n            Object.keys(url).forEach((key)=>{\n                if (!urlObjectKeys.includes(key)) {\n                    console.warn(\"Unknown key passed via urlObject into url.format: \" + key);\n                }\n            });\n        }\n    }\n    return formatUrl(url);\n} //# sourceMappingURL=format-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/is-local-url.js ***!
  \************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isLocalURL\", ({\n    enumerable: true,\n    get: function() {\n        return isLocalURL;\n    }\n}));\nconst _utils = __webpack_require__(/*! ../../utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../../client/has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nfunction isLocalURL(url) {\n    // prevent a hydration mismatch on href for url with anchor refs\n    if (!(0, _utils.isAbsoluteUrl)(url)) return true;\n    try {\n        // absolute urls can be local if they are on the same origin\n        const locationOrigin = (0, _utils.getLocationOrigin)();\n        const resolved = new URL(url, locationOrigin);\n        return resolved.origin === locationOrigin && (0, _hasbasepath.hasBasePath)(resolved.pathname);\n    } catch (_) {\n        return false;\n    }\n} //# sourceMappingURL=is-local-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaXMtbG9jYWwtdXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OENBTWdCQTs7O2VBQUFBOzs7bUNBTmlDO3lDQUNyQjtBQUtyQixTQUFTQSxXQUFXQyxHQUFXO0lBQ3BDLGdFQUFnRTtJQUNoRSxJQUFJLENBQUNDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQWNELE1BQU0sT0FBTztJQUNoQyxJQUFJO1FBQ0YsNERBQTREO1FBQzVELE1BQU1FLGlCQUFpQkMsQ0FBQUEsR0FBQUEsT0FBQUEsaUJBQUFBO1FBQ3ZCLE1BQU1DLFdBQVcsSUFBSUMsSUFBSUwsS0FBS0U7UUFDOUIsT0FBT0UsU0FBU0UsTUFBTSxLQUFLSixrQkFBa0JLLENBQUFBLEdBQUFBLGFBQUFBLFdBQUFBLEVBQVlILFNBQVNJLFFBQVE7SUFDNUUsRUFBRSxPQUFPQyxHQUFHO1FBQ1YsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIi9zcmMvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaXMtbG9jYWwtdXJsLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzQWJzb2x1dGVVcmwsIGdldExvY2F0aW9uT3JpZ2luIH0gZnJvbSAnLi4vLi4vdXRpbHMnXG5pbXBvcnQgeyBoYXNCYXNlUGF0aCB9IGZyb20gJy4uLy4uLy4uLy4uL2NsaWVudC9oYXMtYmFzZS1wYXRoJ1xuXG4vKipcbiAqIERldGVjdHMgd2hldGhlciBhIGdpdmVuIHVybCBpcyByb3V0YWJsZSBieSB0aGUgTmV4dC5qcyByb3V0ZXIgKGJyb3dzZXIgb25seSkuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0xvY2FsVVJMKHVybDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIC8vIHByZXZlbnQgYSBoeWRyYXRpb24gbWlzbWF0Y2ggb24gaHJlZiBmb3IgdXJsIHdpdGggYW5jaG9yIHJlZnNcbiAgaWYgKCFpc0Fic29sdXRlVXJsKHVybCkpIHJldHVybiB0cnVlXG4gIHRyeSB7XG4gICAgLy8gYWJzb2x1dGUgdXJscyBjYW4gYmUgbG9jYWwgaWYgdGhleSBhcmUgb24gdGhlIHNhbWUgb3JpZ2luXG4gICAgY29uc3QgbG9jYXRpb25PcmlnaW4gPSBnZXRMb2NhdGlvbk9yaWdpbigpXG4gICAgY29uc3QgcmVzb2x2ZWQgPSBuZXcgVVJMKHVybCwgbG9jYXRpb25PcmlnaW4pXG4gICAgcmV0dXJuIHJlc29sdmVkLm9yaWdpbiA9PT0gbG9jYXRpb25PcmlnaW4gJiYgaGFzQmFzZVBhdGgocmVzb2x2ZWQucGF0aG5hbWUpXG4gIH0gY2F0Y2ggKF8pIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuIl0sIm5hbWVzIjpbImlzTG9jYWxVUkwiLCJ1cmwiLCJpc0Fic29sdXRlVXJsIiwibG9jYXRpb25PcmlnaW4iLCJnZXRMb2NhdGlvbk9yaWdpbiIsInJlc29sdmVkIiwiVVJMIiwib3JpZ2luIiwiaGFzQmFzZVBhdGgiLCJwYXRobmFtZSIsIl8iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/querystring.js ***!
  \***********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    assign: function() {\n        return assign;\n    },\n    searchParamsToUrlQuery: function() {\n        return searchParamsToUrlQuery;\n    },\n    urlQueryToSearchParams: function() {\n        return urlQueryToSearchParams;\n    }\n});\nfunction searchParamsToUrlQuery(searchParams) {\n    const query = {};\n    for (const [key, value] of searchParams.entries()){\n        const existing = query[key];\n        if (typeof existing === 'undefined') {\n            query[key] = value;\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            query[key] = [\n                existing,\n                value\n            ];\n        }\n    }\n    return query;\n}\nfunction stringifyUrlQueryParam(param) {\n    if (typeof param === 'string') {\n        return param;\n    }\n    if (typeof param === 'number' && !isNaN(param) || typeof param === 'boolean') {\n        return String(param);\n    } else {\n        return '';\n    }\n}\nfunction urlQueryToSearchParams(query) {\n    const searchParams = new URLSearchParams();\n    for (const [key, value] of Object.entries(query)){\n        if (Array.isArray(value)) {\n            for (const item of value){\n                searchParams.append(key, stringifyUrlQueryParam(item));\n            }\n        } else {\n            searchParams.set(key, stringifyUrlQueryParam(value));\n        }\n    }\n    return searchParams;\n}\nfunction assign(target) {\n    for(var _len = arguments.length, searchParamsList = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        searchParamsList[_key - 1] = arguments[_key];\n    }\n    for (const searchParams of searchParamsList){\n        for (const key of searchParams.keys()){\n            target.delete(key);\n        }\n        for (const [key, value] of searchParams.entries()){\n            target.append(key, value);\n        }\n    }\n    return target;\n} //# sourceMappingURL=querystring.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    'CLS',\n    'FCP',\n    'FID',\n    'INP',\n    'LCP',\n    'TTFB'\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? ':' + port : '');\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split('?');\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, '/').replace(/\\/\\/+/g, '/') + (urlParts[1] ? \"?\" + urlParts.slice(1).join('?') : '');\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== 'undefined';\nconst ST = SP && [\n    'mark',\n    'measure',\n    'getEntriesByName'\n].every((method)=>typeof performance[method] === 'function');\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = 'ENOENT';\n        this.name = 'PageNotFoundError';\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = 'ENOENT';\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/error-once.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"errorOnce\", ({\n    enumerable: true,\n    get: function() {\n        return errorOnce;\n    }\n}));\nlet errorOnce = (_)=>{};\nif (true) {\n    const errors = new Set();\n    errorOnce = (msg)=>{\n        if (!errors.has(msg)) {\n            console.error(msg);\n        }\n        errors.add(msg);\n    };\n} //# sourceMappingURL=error-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy9lcnJvci1vbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7NkNBV1NBOzs7ZUFBQUE7OztBQVhULElBQUlBLFlBQVksQ0FBQ0MsS0FBZTtBQUNoQyxJQUFJQyxJQUFvQixFQUFtQjtJQUN6QyxNQUFNRyxTQUFTLElBQUlDO0lBQ25CTixZQUFZLENBQUNPO1FBQ1gsSUFBSSxDQUFDRixPQUFPRyxHQUFHLENBQUNELE1BQU07WUFDcEJFLFFBQVFDLEtBQUssQ0FBQ0g7UUFDaEI7UUFDQUYsT0FBT00sR0FBRyxDQUFDSjtJQUNiO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zcmMvc2hhcmVkL2xpYi91dGlscy9lcnJvci1vbmNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImxldCBlcnJvck9uY2UgPSAoXzogc3RyaW5nKSA9PiB7fVxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgY29uc3QgZXJyb3JzID0gbmV3IFNldDxzdHJpbmc+KClcbiAgZXJyb3JPbmNlID0gKG1zZzogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFlcnJvcnMuaGFzKG1zZykpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IobXNnKVxuICAgIH1cbiAgICBlcnJvcnMuYWRkKG1zZylcbiAgfVxufVxuXG5leHBvcnQgeyBlcnJvck9uY2UgfVxuIl0sIm5hbWVzIjpbImVycm9yT25jZSIsIl8iLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJlcnJvcnMiLCJTZXQiLCJtc2ciLCJoYXMiLCJjb25zb2xlIiwiZXJyb3IiLCJhZGQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);