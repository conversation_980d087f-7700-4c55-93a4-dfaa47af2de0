globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/docs/installation/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/docs/introduction/page.tsx":{"*":{"id":"(ssr)/./app/docs/introduction/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/docs/foundations/colors/page.tsx":{"*":{"id":"(ssr)/./app/docs/foundations/colors/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/docs/shared-components/buttons/page.tsx":{"*":{"id":"(ssr)/./app/docs/shared-components/buttons/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/docs/application-components/charts/page.tsx":{"*":{"id":"(ssr)/./app/docs/application-components/charts/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/docs/foundations/typography/page.tsx":{"*":{"id":"(ssr)/./app/docs/foundations/typography/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/docs/foundations/shadows-blurs/page.tsx":{"*":{"id":"(ssr)/./app/docs/foundations/shadows-blurs/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/docs/foundations/design-annotations/page.tsx":{"*":{"id":"(ssr)/./app/docs/foundations/design-annotations/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/Layout.tsx":{"*":{"id":"(ssr)/./components/Layout.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Documents/Projects/D system/app/globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/client/app-dir/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/esm/client/app-dir/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Projects/D system/app/docs/introduction/page.tsx":{"id":"(app-pages-browser)/./app/docs/introduction/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/colors/page.tsx":{"id":"(app-pages-browser)/./app/docs/foundations/colors/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Projects/D system/app/docs/shared-components/buttons/page.tsx":{"id":"(app-pages-browser)/./app/docs/shared-components/buttons/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Projects/D system/app/docs/application-components/charts/page.tsx":{"id":"(app-pages-browser)/./app/docs/application-components/charts/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/typography/page.tsx":{"id":"(app-pages-browser)/./app/docs/foundations/typography/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx":{"id":"(app-pages-browser)/./app/docs/foundations/shadows-blurs/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/design-annotations/page.tsx":{"id":"(app-pages-browser)/./app/docs/foundations/design-annotations/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx":{"id":"(app-pages-browser)/./components/Layout.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/Documents/Projects/D system/":[],"/Users/<USER>/Documents/Projects/D system/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/Users/<USER>/Documents/Projects/D system/app/page":[],"/Users/<USER>/Documents/Projects/D system/app/docs/installation/page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/docs/introduction/page.tsx":{"*":{"id":"(rsc)/./app/docs/introduction/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/docs/foundations/colors/page.tsx":{"*":{"id":"(rsc)/./app/docs/foundations/colors/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/docs/shared-components/buttons/page.tsx":{"*":{"id":"(rsc)/./app/docs/shared-components/buttons/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/docs/application-components/charts/page.tsx":{"*":{"id":"(rsc)/./app/docs/application-components/charts/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/docs/foundations/typography/page.tsx":{"*":{"id":"(rsc)/./app/docs/foundations/typography/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/docs/foundations/shadows-blurs/page.tsx":{"*":{"id":"(rsc)/./app/docs/foundations/shadows-blurs/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/docs/foundations/design-annotations/page.tsx":{"*":{"id":"(rsc)/./app/docs/foundations/design-annotations/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/Layout.tsx":{"*":{"id":"(rsc)/./components/Layout.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}