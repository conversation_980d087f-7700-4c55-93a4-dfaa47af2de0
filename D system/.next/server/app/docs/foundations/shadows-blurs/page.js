/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/docs/foundations/shadows-blurs/page";
exports.ids = ["app/docs/foundations/shadows-blurs/page"];
exports.modules = {

/***/ "(rsc)/./app/docs/foundations/shadows-blurs/page.tsx":
/*!*****************************************************!*\
  !*** ./app/docs/foundations/shadows-blurs/page.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cc69884ed9b6\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYWhtb3VkbWV0d2FseS9Eb2N1bWVudHMvUHJvamVjdHMvRCBzeXN0ZW0vYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiY2M2OTg4NGVkOWI2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: 'Design System - Chakra UI RSC Library',\n    description: 'A comprehensive design system built with Next.js 15, React 19, and Chakra UI 3.3',\n    keywords: [\n        'design system',\n        'chakra ui',\n        'react',\n        'next.js',\n        'figma',\n        'components'\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/app/layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/app/layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDdUI7QUFFaEIsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO1FBQUM7UUFBaUI7UUFBYTtRQUFTO1FBQVc7UUFBUztLQUFhO0FBQ3JGLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyx3QkFBd0I7a0JBQ3RDLDRFQUFDQztzQkFDRUo7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYWhtb3VkbWV0d2FseS9Eb2N1bWVudHMvUHJvamVjdHMvRCBzeXN0ZW0vYXBwL2xheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJztcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdEZXNpZ24gU3lzdGVtIC0gQ2hha3JhIFVJIFJTQyBMaWJyYXJ5JyxcbiAgZGVzY3JpcHRpb246ICdBIGNvbXByZWhlbnNpdmUgZGVzaWduIHN5c3RlbSBidWlsdCB3aXRoIE5leHQuanMgMTUsIFJlYWN0IDE5LCBhbmQgQ2hha3JhIFVJIDMuMycsXG4gIGtleXdvcmRzOiBbJ2Rlc2lnbiBzeXN0ZW0nLCAnY2hha3JhIHVpJywgJ3JlYWN0JywgJ25leHQuanMnLCAnZmlnbWEnLCAnY29tcG9uZW50cyddLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cbiAgICAgIDxib2R5PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage&page=%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage&appPaths=%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage&page=%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage&appPaths=%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/docs/foundations/shadows-blurs/page.tsx */ \"(rsc)/./app/docs/foundations/shadows-blurs/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'docs',\n        {\n        children: [\n        'foundations',\n        {\n        children: [\n        'shadows-blurs',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/Projects/D system/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/docs/foundations/shadows-blurs/page\",\n        pathname: \"/docs/foundations/shadows-blurs\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage&page=%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage&appPaths=%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/docs/foundations/shadows-blurs/page.tsx */ \"(rsc)/./app/docs/foundations/shadows-blurs/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFobW91ZG1ldHdhbHklMkZEb2N1bWVudHMlMkZQcm9qZWN0cyUyRkQlMjBzeXN0ZW0lMkZhcHAlMkZkb2NzJTJGZm91bmRhdGlvbnMlMkZzaGFkb3dzLWJsdXJzJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNNQUFrSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL21haG1vdWRtZXR3YWx5L0RvY3VtZW50cy9Qcm9qZWN0cy9EIHN5c3RlbS9hcHAvZG9jcy9mb3VuZGF0aW9ucy9zaGFkb3dzLWJsdXJzL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/docs/foundations/shadows-blurs/page.tsx":
/*!*****************************************************!*\
  !*** ./app/docs/foundations/shadows-blurs/page.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShadowsBlursPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_DocsLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/DocsLayout */ \"(ssr)/./components/DocsLayout.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ShadowsBlursPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DocsLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"prose prose-lg max-w-none\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"not-prose mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900\",\n                                children: \"Shadows & blurs\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 leading-relaxed\",\n                            children: \"Essential design tokens and foundational elements for shadows & blurs. These form the building blocks of our design system.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                    children: \"Overview\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"Shadows & blurs are fundamental design tokens that ensure consistency across your application. They provide the visual foundation that all other components build upon.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                    children: \"Usage\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"not-prose bg-gray-900 rounded-lg p-4 my-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"text-green-400 text-sm overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            children: `import { Shadows&blurs } from '@your-org/design-system';\n\nexport function Example() {\n  return (\n    <Shadows&blurs\n      variant=\"primary\"\n      size=\"md\"\n    >\n      Shadows & blurs Example\n    </Shadows&blurs>\n  );\n}`\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                    children: \"Examples\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"not-prose grid grid-cols-1 md:grid-cols-2 gap-6 my-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-gray-900 mb-3\",\n                                    children: \"Default Shadows & blurs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-lg p-4 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-sm\",\n                                        children: \"Preview of shadows & blurs\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-gray-900 mb-3\",\n                                    children: \"Variant Example\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-lg p-4 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-sm\",\n                                        children: \"Alternative shadows & blurs style\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                    children: \"Props & Variants\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"not-prose overflow-x-auto mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full border border-gray-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-4 py-2 text-left text-sm font-medium text-gray-900\",\n                                            children: \"Prop\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-4 py-2 text-left text-sm font-medium text-gray-900\",\n                                            children: \"Type\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-4 py-2 text-left text-sm font-medium text-gray-900\",\n                                            children: \"Default\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-4 py-2 text-left text-sm font-medium text-gray-900\",\n                                            children: \"Description\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                children: \"variant\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"string\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: '\"default\"'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"Visual style variant\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                children: \"size\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"string\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: '\"md\"'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"Component size\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                children: \"disabled\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"boolean\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"false\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"Disable interaction\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                children: \"className\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"string\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-2 text-sm text-gray-600\",\n                                                children: \"Additional CSS classes\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                    children: \"Best Practices\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"list-disc list-inside text-gray-600 space-y-2 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Use shadows & blurs consistently across your application\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Follow accessibility guidelines when implementing\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Test components across different screen sizes\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Maintain proper contrast ratios\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Use semantic HTML elements when possible\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                    children: \"Related Components\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"not-prose bg-gray-50 rounded-lg p-6 mt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Related Components\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/docs/foundations/colors\",\n                                    children: \"Colors\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/docs/foundations/typography\",\n                                    children: \"Typography\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/app/docs/foundations/shadows-blurs/page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/docs/foundations/shadows-blurs/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/DocsLayout.tsx":
/*!***********************************!*\
  !*** ./components/DocsLayout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocsLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// All 94 pages from Figma organized in the same order\nconst docsNavigation = [\n    {\n        title: 'Getting Started',\n        items: [\n            {\n                title: 'Introduction',\n                href: '/docs/introduction'\n            },\n            {\n                title: 'Installation',\n                href: '/docs/installation'\n            },\n            {\n                title: 'Quick Start',\n                href: '/docs/quick-start'\n            }\n        ]\n    },\n    {\n        title: 'Thumbnail',\n        items: [\n            {\n                title: 'Overview',\n                href: '/docs/thumbnail/overview'\n            }\n        ]\n    },\n    {\n        title: 'Foundations',\n        items: [\n            {\n                title: 'Colors ✨ NEW ✨',\n                href: '/docs/foundations/colors'\n            },\n            {\n                title: 'Typography',\n                href: '/docs/foundations/typography'\n            },\n            {\n                title: 'Logos',\n                href: '/docs/foundations/logos'\n            },\n            {\n                title: 'Icons',\n                href: '/docs/foundations/icons'\n            },\n            {\n                title: 'Misc icons ✨ NEW ✨',\n                href: '/docs/foundations/misc-icons'\n            },\n            {\n                title: 'Shadows & blurs',\n                href: '/docs/foundations/shadows-blurs'\n            },\n            {\n                title: 'Grids & spacing',\n                href: '/docs/foundations/grids-spacing'\n            },\n            {\n                title: 'Portfolio mockups',\n                href: '/docs/foundations/portfolio-mockups'\n            },\n            {\n                title: 'Design annotations',\n                href: '/docs/foundations/design-annotations'\n            }\n        ]\n    },\n    {\n        title: 'Shared Components',\n        items: [\n            {\n                title: 'Buttons',\n                href: '/docs/shared-components/buttons'\n            },\n            {\n                title: 'Button groups',\n                href: '/docs/shared-components/button-groups'\n            },\n            {\n                title: 'Badges ✨ NEW ✨',\n                href: '/docs/shared-components/badges'\n            },\n            {\n                title: 'Tags',\n                href: '/docs/shared-components/tags'\n            },\n            {\n                title: 'Dropdowns',\n                href: '/docs/shared-components/dropdowns'\n            },\n            {\n                title: 'Inputs',\n                href: '/docs/shared-components/inputs'\n            },\n            {\n                title: 'Toggles',\n                href: '/docs/shared-components/toggles'\n            },\n            {\n                title: 'Checkboxes',\n                href: '/docs/shared-components/checkboxes'\n            },\n            {\n                title: 'Checkbox groups',\n                href: '/docs/shared-components/checkbox-groups'\n            },\n            {\n                title: 'Avatars ✨ NEW ✨',\n                href: '/docs/shared-components/avatars'\n            },\n            {\n                title: 'Tooltips',\n                href: '/docs/shared-components/tooltips'\n            },\n            {\n                title: 'Progress indicators',\n                href: '/docs/shared-components/progress-indicators'\n            },\n            {\n                title: 'Sliders',\n                href: '/docs/shared-components/sliders'\n            }\n        ]\n    },\n    {\n        title: 'Shared Assets',\n        items: [\n            {\n                title: 'Log in and sign up pages ✨ NEW ✨',\n                href: '/docs/shared-assets/login-signup-pages'\n            },\n            {\n                title: '404 pages ✨ NEW ✨',\n                href: '/docs/shared-assets/404-pages'\n            },\n            {\n                title: 'Email templates',\n                href: '/docs/shared-assets/email-templates'\n            },\n            {\n                title: 'Miscellaneous assets',\n                href: '/docs/shared-assets/miscellaneous-assets'\n            },\n            {\n                title: 'Background elements ✨ NEW ✨',\n                href: '/docs/shared-assets/background-elements'\n            }\n        ]\n    },\n    {\n        title: 'Marketing Website Examples',\n        items: [\n            {\n                title: 'Landing pages',\n                href: '/docs/marketing-examples/landing-pages'\n            },\n            {\n                title: 'Pricing pages',\n                href: '/docs/marketing-examples/pricing-pages'\n            },\n            {\n                title: 'Blogs',\n                href: '/docs/marketing-examples/blogs'\n            },\n            {\n                title: 'Blog posts',\n                href: '/docs/marketing-examples/blog-posts'\n            },\n            {\n                title: 'About pages',\n                href: '/docs/marketing-examples/about-pages'\n            },\n            {\n                title: 'Contact pages',\n                href: '/docs/marketing-examples/contact-pages'\n            },\n            {\n                title: 'Team pages',\n                href: '/docs/marketing-examples/team-pages'\n            },\n            {\n                title: 'Legal pages',\n                href: '/docs/marketing-examples/legal-pages'\n            },\n            {\n                title: 'FAQ pages',\n                href: '/docs/marketing-examples/faq-pages'\n            },\n            {\n                title: 'Log in and sign up pages',\n                href: '/docs/marketing-examples/login-signup'\n            },\n            {\n                title: '404 pages',\n                href: '/docs/marketing-examples/404-pages'\n            }\n        ]\n    },\n    {\n        title: 'Marketing Website Components',\n        items: [\n            {\n                title: 'Header navigation',\n                href: '/docs/marketing-components/header-navigation'\n            },\n            {\n                title: 'Header sections',\n                href: '/docs/marketing-components/header-sections'\n            },\n            {\n                title: 'Features sections',\n                href: '/docs/marketing-components/features-sections'\n            },\n            {\n                title: 'Pricing sections',\n                href: '/docs/marketing-components/pricing-sections'\n            },\n            {\n                title: 'CTA sections',\n                href: '/docs/marketing-components/cta-sections'\n            },\n            {\n                title: 'Metrics sections',\n                href: '/docs/marketing-components/metrics-sections'\n            },\n            {\n                title: 'Newsletter CTA sections',\n                href: '/docs/marketing-components/newsletter-cta-sections'\n            },\n            {\n                title: 'Testimonial sections',\n                href: '/docs/marketing-components/testimonial-sections'\n            },\n            {\n                title: 'Social proof sections',\n                href: '/docs/marketing-components/social-proof-sections'\n            },\n            {\n                title: 'Blog sections ✨ NEW ✨',\n                href: '/docs/marketing-components/blog-sections'\n            },\n            {\n                title: 'Content',\n                href: '/docs/marketing-components/content'\n            },\n            {\n                title: 'Contact sections',\n                href: '/docs/marketing-components/contact-sections'\n            },\n            {\n                title: 'Team sections',\n                href: '/docs/marketing-components/team-sections'\n            },\n            {\n                title: 'Careers sections',\n                href: '/docs/marketing-components/careers-sections'\n            },\n            {\n                title: 'FAQ sections',\n                href: '/docs/marketing-components/faq-sections'\n            },\n            {\n                title: 'Footers',\n                href: '/docs/marketing-components/footers'\n            },\n            {\n                title: 'Banners',\n                href: '/docs/marketing-components/banners'\n            }\n        ]\n    },\n    {\n        title: 'Application Examples',\n        items: [\n            {\n                title: 'Dashboards',\n                href: '/docs/application-examples/dashboards'\n            },\n            {\n                title: 'Settings pages',\n                href: '/docs/application-examples/settings-pages'\n            },\n            {\n                title: 'Informational pages',\n                href: '/docs/application-examples/informational-pages'\n            }\n        ]\n    },\n    {\n        title: 'Application Components',\n        items: [\n            {\n                title: 'Page headers',\n                href: '/docs/application-components/page-headers'\n            },\n            {\n                title: 'Card headers',\n                href: '/docs/application-components/card-headers'\n            },\n            {\n                title: 'Section headers ✨ NEW ✨',\n                href: '/docs/application-components/section-headers'\n            },\n            {\n                title: 'Section footers',\n                href: '/docs/application-components/section-footers'\n            },\n            {\n                title: 'Application navigation',\n                href: '/docs/application-components/application-navigation'\n            },\n            {\n                title: 'Modals ✨ NEW ✨',\n                href: '/docs/application-components/modals'\n            },\n            {\n                title: 'Command menus ✨ NEW ✨',\n                href: '/docs/application-components/command-menus'\n            },\n            {\n                title: 'Charts ✨ NEW ✨',\n                href: '/docs/application-components/charts'\n            },\n            {\n                title: 'Metrics ✨ NEW ✨',\n                href: '/docs/application-components/metrics'\n            },\n            {\n                title: 'Slideout menus',\n                href: '/docs/application-components/slideout-menus'\n            },\n            {\n                title: 'Inline CTAs',\n                href: '/docs/application-components/inline-ctas'\n            },\n            {\n                title: 'Pagination',\n                href: '/docs/application-components/pagination'\n            },\n            {\n                title: 'Progress steps ✨ NEW ✨',\n                href: '/docs/application-components/progress-steps'\n            },\n            {\n                title: 'Activity feeds ✨ NEW ✨',\n                href: '/docs/application-components/activity-feeds'\n            },\n            {\n                title: 'Messaging',\n                href: '/docs/application-components/messaging'\n            },\n            {\n                title: 'Tabs ✨ NEW ✨',\n                href: '/docs/application-components/tabs'\n            },\n            {\n                title: 'Tables',\n                href: '/docs/application-components/tables'\n            },\n            {\n                title: 'Breadcrumbs',\n                href: '/docs/application-components/breadcrumbs'\n            },\n            {\n                title: 'Alerts & notifications ✨ NEW ✨',\n                href: '/docs/application-components/alerts-notifications'\n            },\n            {\n                title: 'Date pickers',\n                href: '/docs/application-components/date-pickers'\n            },\n            {\n                title: 'File upload ✨ NEW ✨',\n                href: '/docs/application-components/file-upload'\n            },\n            {\n                title: 'Content dividers',\n                href: '/docs/application-components/content-dividers'\n            },\n            {\n                title: 'Loading indicators',\n                href: '/docs/application-components/loading-indicators'\n            },\n            {\n                title: 'Empty states ✨ NEW ✨',\n                href: '/docs/application-components/empty-states'\n            },\n            {\n                title: 'Code snippets',\n                href: '/docs/application-components/code-snippets'\n            }\n        ]\n    }\n];\nfunction DocsLayout({ children }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white border-b border-gray-200 sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                        className: \"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 6h16M4 12h16M4 18h16\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center ml-4 lg:ml-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-purple-600 to-teal-600 rounded-lg flex items-center justify-center mr-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-sm\",\n                                                    children: \"DS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: \"Design System\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/\",\n                                        className: \"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/figma-sync\",\n                                        className: \"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\",\n                                        children: \"Figma Sync\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: `${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0 fixed lg:static top-16 lg:top-0 inset-y-0 left-0 z-40 w-64 bg-white border-r border-gray-200 transition-transform duration-300 ease-in-out lg:transition-none`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto pt-6 pb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"px-3\",\n                                children: docsNavigation.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3\",\n                                                children: section.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1\",\n                                                children: section.items.map((item)=>{\n                                                    const isActive = pathname === item.href;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: item.href,\n                                                            className: `${isActive ? 'bg-purple-50 border-r-2 border-purple-500 text-purple-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'} group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors`,\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, item.href, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, section.title, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:hidden fixed inset-0 z-30 bg-gray-600 bg-opacity-50\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 min-w-0 lg:ml-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/DocsLayout.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/DocsLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/docs/foundations/shadows-blurs/page.tsx */ \"(ssr)/./app/docs/foundations/shadows-blurs/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFobW91ZG1ldHdhbHklMkZEb2N1bWVudHMlMkZQcm9qZWN0cyUyRkQlMjBzeXN0ZW0lMkZhcHAlMkZkb2NzJTJGZm91bmRhdGlvbnMlMkZzaGFkb3dzLWJsdXJzJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNNQUFrSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL21haG1vdWRtZXR3YWx5L0RvY3VtZW50cy9Qcm9qZWN0cy9EIHN5c3RlbS9hcHAvZG9jcy9mb3VuZGF0aW9ucy9zaGFkb3dzLWJsdXJzL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage&page=%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage&appPaths=%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Ffoundations%2Fshadows-blurs%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();