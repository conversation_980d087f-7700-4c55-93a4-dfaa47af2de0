/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e3aa7206e54a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYWhtb3VkbWV0d2FseS9Eb2N1bWVudHMvUHJvamVjdHMvRCBzeXN0ZW0vYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZTNhYTcyMDZlNTRhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: 'Design System - Chakra UI RSC Library',\n    description: 'A comprehensive design system built with Next.js 15, React 19, and Chakra UI 3.3',\n    keywords: [\n        'design system',\n        'chakra ui',\n        'react',\n        'next.js',\n        'figma',\n        'components'\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/app/layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/app/layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDdUI7QUFFaEIsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO1FBQUM7UUFBaUI7UUFBYTtRQUFTO1FBQVc7UUFBUztLQUFhO0FBQ3JGLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyx3QkFBd0I7a0JBQ3RDLDRFQUFDQztzQkFDRUo7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYWhtb3VkbWV0d2FseS9Eb2N1bWVudHMvUHJvamVjdHMvRCBzeXN0ZW0vYXBwL2xheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJztcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdEZXNpZ24gU3lzdGVtIC0gQ2hha3JhIFVJIFJTQyBMaWJyYXJ5JyxcbiAgZGVzY3JpcHRpb246ICdBIGNvbXByZWhlbnNpdmUgZGVzaWduIHN5c3RlbSBidWlsdCB3aXRoIE5leHQuanMgMTUsIFJlYWN0IDE5LCBhbmQgQ2hha3JhIFVJIDMuMycsXG4gIGtleXdvcmRzOiBbJ2Rlc2lnbiBzeXN0ZW0nLCAnY2hha3JhIHVpJywgJ3JlYWN0JywgJ25leHQuanMnLCAnZmlnbWEnLCAnY29tcG9uZW50cyddLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cbiAgICAgIDxib2R5PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"(rsc)/./components/Layout.tsx\");\n/* harmony import */ var _lib_design_system__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/design-system */ \"(rsc)/./lib/design-system.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction HomePage() {\n    const totalComponents = _lib_design_system__WEBPACK_IMPORTED_MODULE_2__.designSystemCategories.reduce((total, category)=>total + category.components.length, 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Design System\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto mb-6\",\n                            children: \"A comprehensive design system built with Next.js 15, React 19, and Chakra UI 3.3. Featuring Figma integration and React Server Components.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"badge badge-blue px-3 py-1\",\n                                    children: \"Next.js 15\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"badge badge-green px-3 py-1\",\n                                    children: \"React 19\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"badge badge-purple px-3 py-1\",\n                                    children: \"Chakra UI 3.3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"badge badge-orange px-3 py-1\",\n                                    children: \"Figma Integration\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card text-center py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-blue-600\",\n                                    children: _lib_design_system__WEBPACK_IMPORTED_MODULE_2__.designSystemCategories.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Categories\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card text-center py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-blue-600\",\n                                    children: totalComponents\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Components\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card text-center py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-blue-600\",\n                                    children: \"RSC\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Server Components\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-6\",\n                            children: \"Categories\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: _lib_design_system__WEBPACK_IMPORTED_MODULE_2__.designSystemCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: `/${category.id}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card h-full cursor-pointer transition hover:shadow-lg hover:translate-y-[-2px]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-blue-50 rounded text-blue-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5\",\n                                                                viewBox: \"0 0 16 16\",\n                                                                fill: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M4 2a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h8v8H4V4z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                                    lineNumber: 74,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                                lineNumber: 73,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                            lineNumber: 72,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"badge badge-gray\",\n                                                            children: [\n                                                                category.components.length,\n                                                                \" components\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                            lineNumber: 77,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                            children: category.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: category.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this)\n                                }, category.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-6\",\n                            children: \"Getting Started\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                            children: \"Explore Components\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Browse through our comprehensive collection of components organized by category. Each component includes multiple variants and usage examples.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/foundations\",\n                                            className: \"text-blue-600 font-medium hover:text-blue-700\",\n                                            children: \"Start with Foundations →\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                            children: \"Figma Integration\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Our design system is connected to Figma, allowing you to sync design tokens and convert designs into React Server Components.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/shared-components\",\n                                            className: \"text-blue-600 font-medium hover:text-blue-700\",\n                                            children: \"View Components →\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./lib/design-system.ts":
/*!******************************!*\
  !*** ./lib/design-system.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   designSystemCategories: () => (/* binding */ designSystemCategories),\n/* harmony export */   getAllComponents: () => (/* binding */ getAllComponents),\n/* harmony export */   getCategoryById: () => (/* binding */ getCategoryById),\n/* harmony export */   getComponentById: () => (/* binding */ getComponentById),\n/* harmony export */   getComponentsByCategory: () => (/* binding */ getComponentsByCategory)\n/* harmony export */ });\n// Design System Configuration based on Figma file\nconst designSystemCategories = [\n    {\n        id: 'thumbnail',\n        title: 'Thumbnail',\n        description: 'Overview and preview of the design system',\n        components: [\n            {\n                id: 'overview',\n                name: 'Design System Overview',\n                category: 'thumbnail',\n                description: 'Complete overview of the design system components and guidelines',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=2210-441096',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {}\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'foundations',\n        title: 'Foundations',\n        description: 'Core design tokens, colors, typography, and spacing',\n        components: [\n            {\n                id: 'colors',\n                name: 'Color System',\n                category: 'foundations',\n                description: 'Primary, secondary, and semantic color palettes with accessibility guidelines',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=colors',\n                variants: [\n                    {\n                        name: 'Primary Palette',\n                        props: {\n                            palette: 'primary'\n                        }\n                    },\n                    {\n                        name: 'Secondary Palette',\n                        props: {\n                            palette: 'secondary'\n                        }\n                    },\n                    {\n                        name: 'Semantic Colors',\n                        props: {\n                            palette: 'semantic'\n                        }\n                    },\n                    {\n                        name: 'Neutral Colors',\n                        props: {\n                            palette: 'neutral'\n                        }\n                    },\n                    {\n                        name: 'Brand Colors',\n                        props: {\n                            palette: 'brand'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'primary-50',\n                        value: '#eff6ff',\n                        type: 'color',\n                        description: 'Primary color lightest shade'\n                    },\n                    {\n                        name: 'primary-500',\n                        value: '#3b82f6',\n                        type: 'color',\n                        description: 'Primary color base'\n                    },\n                    {\n                        name: 'primary-900',\n                        value: '#1e3a8a',\n                        type: 'color',\n                        description: 'Primary color darkest shade'\n                    }\n                ]\n            },\n            {\n                id: 'typography',\n                name: 'Typography Scale',\n                category: 'foundations',\n                description: 'Font families, sizes, weights, and line heights for consistent text hierarchy',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=typography',\n                variants: [\n                    {\n                        name: 'Display Text',\n                        props: {\n                            category: 'display'\n                        }\n                    },\n                    {\n                        name: 'Headings',\n                        props: {\n                            category: 'headings'\n                        }\n                    },\n                    {\n                        name: 'Body Text',\n                        props: {\n                            category: 'body'\n                        }\n                    },\n                    {\n                        name: 'Labels & Captions',\n                        props: {\n                            category: 'labels'\n                        }\n                    },\n                    {\n                        name: 'Code & Monospace',\n                        props: {\n                            category: 'code'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'font-size-xs',\n                        value: '12px',\n                        type: 'typography',\n                        description: 'Extra small text size'\n                    },\n                    {\n                        name: 'font-size-base',\n                        value: '16px',\n                        type: 'typography',\n                        description: 'Base text size'\n                    },\n                    {\n                        name: 'font-size-2xl',\n                        value: '24px',\n                        type: 'typography',\n                        description: 'Large heading size'\n                    }\n                ]\n            },\n            {\n                id: 'spacing',\n                name: 'Spacing System',\n                category: 'foundations',\n                description: '8px grid system for consistent spacing and layout',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=spacing',\n                variants: [\n                    {\n                        name: 'Base Scale',\n                        props: {\n                            scale: 'base'\n                        }\n                    },\n                    {\n                        name: 'Component Spacing',\n                        props: {\n                            scale: 'component'\n                        }\n                    },\n                    {\n                        name: 'Layout Spacing',\n                        props: {\n                            scale: 'layout'\n                        }\n                    },\n                    {\n                        name: 'Responsive Spacing',\n                        props: {\n                            scale: 'responsive'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'space-1',\n                        value: '4px',\n                        type: 'spacing',\n                        description: 'Smallest spacing unit'\n                    },\n                    {\n                        name: 'space-4',\n                        value: '16px',\n                        type: 'spacing',\n                        description: 'Base spacing unit'\n                    },\n                    {\n                        name: 'space-8',\n                        value: '32px',\n                        type: 'spacing',\n                        description: 'Large spacing unit'\n                    }\n                ]\n            },\n            {\n                id: 'elevation',\n                name: 'Elevation & Shadows',\n                category: 'foundations',\n                description: 'Shadow system for creating depth and visual hierarchy',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=elevation',\n                variants: [\n                    {\n                        name: 'Card Shadows',\n                        props: {\n                            type: 'card'\n                        }\n                    },\n                    {\n                        name: 'Modal Shadows',\n                        props: {\n                            type: 'modal'\n                        }\n                    },\n                    {\n                        name: 'Focus Rings',\n                        props: {\n                            type: 'focus'\n                        }\n                    },\n                    {\n                        name: 'Dropdown Shadows',\n                        props: {\n                            type: 'dropdown'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'shadow-sm',\n                        value: '0 1px 2px 0 rgb(0 0 0 / 0.05)',\n                        type: 'shadow',\n                        description: 'Small shadow'\n                    },\n                    {\n                        name: 'shadow-md',\n                        value: '0 4px 6px -1px rgb(0 0 0 / 0.1)',\n                        type: 'shadow',\n                        description: 'Medium shadow'\n                    },\n                    {\n                        name: 'shadow-lg',\n                        value: '0 10px 15px -3px rgb(0 0 0 / 0.1)',\n                        type: 'shadow',\n                        description: 'Large shadow'\n                    }\n                ]\n            },\n            {\n                id: 'grid',\n                name: 'Grid System',\n                category: 'foundations',\n                description: 'Responsive grid system and layout guidelines',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=grid',\n                variants: [\n                    {\n                        name: '12 Column Grid',\n                        props: {\n                            columns: 12\n                        }\n                    },\n                    {\n                        name: 'Responsive Breakpoints',\n                        props: {\n                            type: 'breakpoints'\n                        }\n                    },\n                    {\n                        name: 'Container Sizes',\n                        props: {\n                            type: 'containers'\n                        }\n                    },\n                    {\n                        name: 'Gutters & Margins',\n                        props: {\n                            type: 'gutters'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'shared-components',\n        title: 'Shared Components',\n        description: 'Reusable UI components used across applications',\n        components: [\n            {\n                id: 'button',\n                name: 'Button',\n                category: 'shared-components',\n                description: 'Primary interactive element with multiple variants and states',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=button',\n                variants: [\n                    {\n                        name: 'Primary',\n                        props: {\n                            variant: 'primary',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Secondary',\n                        props: {\n                            variant: 'secondary',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Outline',\n                        props: {\n                            variant: 'outline',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Ghost',\n                        props: {\n                            variant: 'ghost',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Destructive',\n                        props: {\n                            variant: 'destructive',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Small',\n                        props: {\n                            variant: 'primary',\n                            size: 'sm'\n                        }\n                    },\n                    {\n                        name: 'Large',\n                        props: {\n                            variant: 'primary',\n                            size: 'lg'\n                        }\n                    },\n                    {\n                        name: 'Icon Only',\n                        props: {\n                            variant: 'primary',\n                            iconOnly: true\n                        }\n                    },\n                    {\n                        name: 'Loading',\n                        props: {\n                            variant: 'primary',\n                            loading: true\n                        }\n                    },\n                    {\n                        name: 'Disabled',\n                        props: {\n                            variant: 'primary',\n                            disabled: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'input',\n                name: 'Input Field',\n                category: 'shared-components',\n                description: 'Text input with labels, validation, and helper text',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=input',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {\n                            placeholder: 'Enter text...'\n                        }\n                    },\n                    {\n                        name: 'With Label',\n                        props: {\n                            label: 'Email Address',\n                            placeholder: 'Enter your email'\n                        }\n                    },\n                    {\n                        name: 'Required',\n                        props: {\n                            label: 'Password',\n                            required: true,\n                            type: 'password'\n                        }\n                    },\n                    {\n                        name: 'Error State',\n                        props: {\n                            label: 'Username',\n                            error: 'Username is already taken'\n                        }\n                    },\n                    {\n                        name: 'Success State',\n                        props: {\n                            label: 'Email',\n                            success: 'Email is available'\n                        }\n                    },\n                    {\n                        name: 'Disabled',\n                        props: {\n                            label: 'Disabled Field',\n                            disabled: true\n                        }\n                    },\n                    {\n                        name: 'With Icon',\n                        props: {\n                            label: 'Search',\n                            icon: 'search',\n                            placeholder: 'Search...'\n                        }\n                    },\n                    {\n                        name: 'Textarea',\n                        props: {\n                            label: 'Message',\n                            type: 'textarea',\n                            rows: 4\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'card',\n                name: 'Card',\n                category: 'shared-components',\n                description: 'Flexible container for grouping related content',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=card',\n                variants: [\n                    {\n                        name: 'Basic Card',\n                        props: {\n                            variant: 'default'\n                        }\n                    },\n                    {\n                        name: 'Elevated Card',\n                        props: {\n                            variant: 'elevated'\n                        }\n                    },\n                    {\n                        name: 'Outlined Card',\n                        props: {\n                            variant: 'outlined'\n                        }\n                    },\n                    {\n                        name: 'Interactive Card',\n                        props: {\n                            variant: 'interactive',\n                            clickable: true\n                        }\n                    },\n                    {\n                        name: 'Product Card',\n                        props: {\n                            variant: 'product',\n                            image: true,\n                            badge: true\n                        }\n                    },\n                    {\n                        name: 'Profile Card',\n                        props: {\n                            variant: 'profile',\n                            avatar: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'badge',\n                name: 'Badge',\n                category: 'shared-components',\n                description: 'Small status indicators and labels',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=badge',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {\n                            variant: 'default'\n                        }\n                    },\n                    {\n                        name: 'Primary',\n                        props: {\n                            variant: 'primary'\n                        }\n                    },\n                    {\n                        name: 'Success',\n                        props: {\n                            variant: 'success'\n                        }\n                    },\n                    {\n                        name: 'Warning',\n                        props: {\n                            variant: 'warning'\n                        }\n                    },\n                    {\n                        name: 'Error',\n                        props: {\n                            variant: 'error'\n                        }\n                    },\n                    {\n                        name: 'Outline',\n                        props: {\n                            variant: 'outline'\n                        }\n                    },\n                    {\n                        name: 'Dot Indicator',\n                        props: {\n                            variant: 'dot'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'avatar',\n                name: 'Avatar',\n                category: 'shared-components',\n                description: 'User profile pictures and initials',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=avatar',\n                variants: [\n                    {\n                        name: 'Image Avatar',\n                        props: {\n                            type: 'image',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Initials',\n                        props: {\n                            type: 'initials',\n                            initials: 'JD',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Icon Avatar',\n                        props: {\n                            type: 'icon',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Small',\n                        props: {\n                            type: 'image',\n                            size: 'sm'\n                        }\n                    },\n                    {\n                        name: 'Large',\n                        props: {\n                            type: 'image',\n                            size: 'lg'\n                        }\n                    },\n                    {\n                        name: 'With Status',\n                        props: {\n                            type: 'image',\n                            status: 'online',\n                            size: 'md'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'dropdown',\n                name: 'Dropdown Menu',\n                category: 'shared-components',\n                description: 'Contextual menus and select components',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=dropdown',\n                variants: [\n                    {\n                        name: 'Basic Dropdown',\n                        props: {\n                            variant: 'basic'\n                        }\n                    },\n                    {\n                        name: 'With Icons',\n                        props: {\n                            variant: 'icons'\n                        }\n                    },\n                    {\n                        name: 'With Dividers',\n                        props: {\n                            variant: 'dividers'\n                        }\n                    },\n                    {\n                        name: 'Multi-select',\n                        props: {\n                            variant: 'multiselect'\n                        }\n                    },\n                    {\n                        name: 'Searchable',\n                        props: {\n                            variant: 'searchable'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'shared-assets',\n        title: 'Shared Assets',\n        description: 'Icons, illustrations, and other visual assets',\n        components: [\n            {\n                id: 'icons',\n                name: 'Icons',\n                category: 'shared-assets',\n                description: 'Icon library and usage guidelines',\n                variants: [\n                    {\n                        name: 'Interface',\n                        props: {\n                            category: 'interface'\n                        }\n                    },\n                    {\n                        name: 'Actions',\n                        props: {\n                            category: 'actions'\n                        }\n                    },\n                    {\n                        name: 'Status',\n                        props: {\n                            category: 'status'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'illustrations',\n                name: 'Illustrations',\n                category: 'shared-assets',\n                description: 'Illustration library for empty states and onboarding',\n                variants: [\n                    {\n                        name: 'Empty States',\n                        props: {\n                            type: 'empty'\n                        }\n                    },\n                    {\n                        name: 'Onboarding',\n                        props: {\n                            type: 'onboarding'\n                        }\n                    },\n                    {\n                        name: 'Error States',\n                        props: {\n                            type: 'error'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'marketing-website-examples',\n        title: 'Marketing Website Examples',\n        description: 'Complete page examples for marketing websites',\n        components: [\n            {\n                id: 'landing-page',\n                name: 'Landing Page',\n                category: 'marketing-website-examples',\n                description: 'Complete landing page layout with hero, features, and CTA',\n                variants: [\n                    {\n                        name: 'SaaS',\n                        props: {\n                            type: 'saas'\n                        }\n                    },\n                    {\n                        name: 'Product',\n                        props: {\n                            type: 'product'\n                        }\n                    },\n                    {\n                        name: 'Agency',\n                        props: {\n                            type: 'agency'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'pricing-page',\n                name: 'Pricing Page',\n                category: 'marketing-website-examples',\n                description: 'Pricing page with plans and feature comparison',\n                variants: [\n                    {\n                        name: 'Simple',\n                        props: {\n                            layout: 'simple'\n                        }\n                    },\n                    {\n                        name: 'Comparison',\n                        props: {\n                            layout: 'comparison'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'marketing-website-components',\n        title: 'Marketing Website Components',\n        description: 'Specialized components for marketing websites',\n        components: [\n            {\n                id: 'hero-section',\n                name: 'Hero Section',\n                category: 'marketing-website-components',\n                description: 'Hero section with headline, description, and CTA',\n                variants: [\n                    {\n                        name: 'Centered',\n                        props: {\n                            alignment: 'center'\n                        }\n                    },\n                    {\n                        name: 'Left Aligned',\n                        props: {\n                            alignment: 'left'\n                        }\n                    },\n                    {\n                        name: 'With Image',\n                        props: {\n                            hasImage: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'feature-grid',\n                name: 'Feature Grid',\n                category: 'marketing-website-components',\n                description: 'Grid layout for showcasing product features',\n                variants: [\n                    {\n                        name: '3 Column',\n                        props: {\n                            columns: 3\n                        }\n                    },\n                    {\n                        name: '4 Column',\n                        props: {\n                            columns: 4\n                        }\n                    },\n                    {\n                        name: 'With Icons',\n                        props: {\n                            hasIcons: true\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'application-examples',\n        title: 'Application Examples',\n        description: 'Complete application page layouts and flows',\n        components: [\n            {\n                id: 'dashboard',\n                name: 'Dashboard',\n                category: 'application-examples',\n                description: 'Complete dashboard layout with sidebar and content area',\n                variants: [\n                    {\n                        name: 'Analytics',\n                        props: {\n                            type: 'analytics'\n                        }\n                    },\n                    {\n                        name: 'CRM',\n                        props: {\n                            type: 'crm'\n                        }\n                    },\n                    {\n                        name: 'E-commerce',\n                        props: {\n                            type: 'ecommerce'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'settings-page',\n                name: 'Settings Page',\n                category: 'application-examples',\n                description: 'Settings page with tabs and form sections',\n                variants: [\n                    {\n                        name: 'Profile',\n                        props: {\n                            section: 'profile'\n                        }\n                    },\n                    {\n                        name: 'Security',\n                        props: {\n                            section: 'security'\n                        }\n                    },\n                    {\n                        name: 'Billing',\n                        props: {\n                            section: 'billing'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'application-components',\n        title: 'Application Components',\n        description: 'Complex components for application interfaces',\n        components: [\n            {\n                id: 'data-table',\n                name: 'Data Table',\n                category: 'application-components',\n                description: 'Advanced data table with sorting, filtering, and pagination',\n                variants: [\n                    {\n                        name: 'Basic',\n                        props: {\n                            features: [\n                                'sorting'\n                            ]\n                        }\n                    },\n                    {\n                        name: 'Advanced',\n                        props: {\n                            features: [\n                                'sorting',\n                                'filtering',\n                                'pagination'\n                            ]\n                        }\n                    },\n                    {\n                        name: 'Selectable',\n                        props: {\n                            selectable: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'sidebar-navigation',\n                name: 'Sidebar Navigation',\n                category: 'application-components',\n                description: 'Collapsible sidebar navigation for applications',\n                variants: [\n                    {\n                        name: 'Expanded',\n                        props: {\n                            collapsed: false\n                        }\n                    },\n                    {\n                        name: 'Collapsed',\n                        props: {\n                            collapsed: true\n                        }\n                    },\n                    {\n                        name: 'With Submenu',\n                        props: {\n                            hasSubmenu: true\n                        }\n                    }\n                ]\n            }\n        ]\n    }\n];\n// Utility functions\nfunction getCategoryById(id) {\n    return designSystemCategories.find((category)=>category.id === id);\n}\nfunction getComponentById(componentId) {\n    for (const category of designSystemCategories){\n        const component = category.components.find((comp)=>comp.id === componentId);\n        if (component) return component;\n    }\n    return undefined;\n}\nfunction getAllComponents() {\n    return designSystemCategories.flatMap((category)=>category.components);\n}\nfunction getComponentsByCategory(categoryId) {\n    const category = getCategoryById(categoryId);\n    return category?.components || [];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/design-system.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/Projects/D system/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/Projects/D system/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Layout.tsx */ \"(rsc)/./components/Layout.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFobW91ZG1ldHdhbHklMkZEb2N1bWVudHMlMkZQcm9qZWN0cyUyRkQlMjBzeXN0ZW0lMkZjb21wb25lbnRzJTJGTGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZtYWhtb3VkbWV0d2FseSUyRkRvY3VtZW50cyUyRlByb2plY3RzJTJGRCUyMHN5c3RlbSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGYXBwLWRpciUyRmxpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEpBQXlJO0FBQ3pJO0FBQ0EsZ05BQThLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL21haG1vdWRtZXR3YWx5L0RvY3VtZW50cy9Qcm9qZWN0cy9EIHN5c3RlbS9jb21wb25lbnRzL0xheW91dC50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIl9fZXNNb2R1bGVcIixcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMvbWFobW91ZG1ldHdhbHkvRG9jdW1lbnRzL1Byb2plY3RzL0Qgc3lzdGVtL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2FwcC1kaXIvbGluay5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./components/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-[280px] min-h-screen bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container py-8 px-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFZ0M7QUFNakIsU0FBU0MsT0FBTyxFQUFFQyxRQUFRLEVBQWU7SUFDdEQscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDSixnREFBT0E7Ozs7OzBCQUdSLDhEQUFDRztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ1pGOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyIvVXNlcnMvbWFobW91ZG1ldHdhbHkvRG9jdW1lbnRzL1Byb2plY3RzL0Qgc3lzdGVtL2NvbXBvbmVudHMvTGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBTaWRlYmFyIGZyb20gJy4vU2lkZWJhcic7XG5cbmludGVyZmFjZSBMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExheW91dCh7IGNoaWxkcmVuIH06IExheW91dFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctd2hpdGVcIj5cbiAgICAgIHsvKiBTaWRlYmFyICovfVxuICAgICAgPFNpZGViYXIgLz5cblxuICAgICAgey8qIE1haW4gQ29udGVudCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtWzI4MHB4XSBtaW4taC1zY3JlZW4gYmctd2hpdGVcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgcHktOCBweC04XCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlNpZGViYXIiLCJMYXlvdXQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/Layout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Sidebar.tsx":
/*!********************************!*\
  !*** ./components/Sidebar.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_design_system__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/design-system */ \"(ssr)/./lib/design-system.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Icons for categories (using simple SVG icons)\nconst CategoryIcons = {\n    thumbnail: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M2 3a1 1 0 011-1h10a1 1 0 011 1v10a1 1 0 01-1 1H3a1 1 0 01-1-1V3zm2 1v8h8V4H4z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined),\n    foundations: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M8 1l7 4v6l-7 4-7-4V5l7-4zm0 2L3.5 5.5 8 8l4.5-2.5L8 3zm-5 4v4l5 2.5V9.5L3 7zm10 0L8 9.5V14l5-2.5V7z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined),\n    'shared-components': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M4 2a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h8v8H4V4z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined),\n    'shared-assets': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M8 1a7 7 0 100 14A7 7 0 008 1zM6 6a1 1 0 112 0 1 1 0 01-2 0zm1 3a1 1 0 100 2 1 1 0 000-2z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined),\n    'marketing-website-examples': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M1 2a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H2a1 1 0 01-1-1V2zm2 1v10h10V3H3z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined),\n    'marketing-website-components': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M2 2h12v2H2V2zm0 4h12v2H2V6zm0 4h8v2H2v-2z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined),\n    'application-examples': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M1 1h14v14H1V1zm2 2v10h10V3H3z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined),\n    'application-components': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M3 3h4v4H3V3zm6 0h4v4H9V3zM3 9h4v4H3V9zm6 0h4v4H9V9z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined)\n};\nfunction Sidebar({ categories = _lib_design_system__WEBPACK_IMPORTED_MODULE_4__.designSystemCategories, currentPath }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'foundations'\n    ]));\n    const toggleCategory = (categoryId)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(categoryId)) {\n            newExpanded.delete(categoryId);\n        } else {\n            newExpanded.add(categoryId);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const isActive = (href)=>{\n        return pathname === href;\n    };\n    const isCategoryActive = (categoryId)=>{\n        return pathname.startsWith(`/${categoryId}`);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed left-0 top-0 z-10 h-screen w-[280px] overflow-y-auto border-r bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"Design System\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-gray-500\",\n                        children: \"Chakra UI RSC Library\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/figma-sync\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        viewBox: \"0 0 16 16\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M8 1a7 7 0 100 14A7 7 0 008 1zM6 6a1 1 0 112 0 1 1 0 01-2 0zm1 3a1 1 0 100 2 1 1 0 000-2z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Figma Sync\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: categories.map((category)=>{\n                    const isExpanded = expandedCategories.has(category.id);\n                    const isCatActive = isCategoryActive(category.id);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>toggleCategory(category.id),\n                                className: `w-full flex items-center justify-between p-3 text-left text-sm font-medium rounded transition-colors ${isCatActive ? 'bg-gray-200 text-gray-900' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-4 h-4\",\n                                                children: CategoryIcons[category.id]\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: category.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `w-3 h-3 transition-transform ${isExpanded ? 'rotate-90' : 'rotate-0'}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            viewBox: \"0 0 16 16\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M6 4l4 4-4 4V4z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 15\n                            }, this),\n                            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-6 py-2\",\n                                children: category.components.map((component)=>{\n                                    const componentHref = `/${category.id}/${component.id}`;\n                                    const isComponentActive = isActive(componentHref);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: componentHref,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `block p-2 text-sm rounded transition-colors ${isComponentActive ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}`,\n                                            children: component.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, component.id, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 23\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, category.id, true, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1NpZGViYXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFaUM7QUFDSjtBQUNpQjtBQUNlO0FBRzdELGdEQUFnRDtBQUNoRCxNQUFNSSxnQkFBZ0I7SUFDcEJDLHlCQUNFLDhEQUFDQztRQUFJQyxPQUFNO1FBQUtDLFFBQU87UUFBS0MsU0FBUTtRQUFZQyxNQUFLO2tCQUNuRCw0RUFBQ0M7WUFBS0MsR0FBRTs7Ozs7Ozs7Ozs7SUFHWkMsMkJBQ0UsOERBQUNQO1FBQUlDLE9BQU07UUFBS0MsUUFBTztRQUFLQyxTQUFRO1FBQVlDLE1BQUs7a0JBQ25ELDRFQUFDQztZQUFLQyxHQUFFOzs7Ozs7Ozs7OztJQUdaLG1DQUNFLDhEQUFDTjtRQUFJQyxPQUFNO1FBQUtDLFFBQU87UUFBS0MsU0FBUTtRQUFZQyxNQUFLO2tCQUNuRCw0RUFBQ0M7WUFBS0MsR0FBRTs7Ozs7Ozs7Ozs7SUFHWiwrQkFDRSw4REFBQ047UUFBSUMsT0FBTTtRQUFLQyxRQUFPO1FBQUtDLFNBQVE7UUFBWUMsTUFBSztrQkFDbkQsNEVBQUNDO1lBQUtDLEdBQUU7Ozs7Ozs7Ozs7O0lBR1osNENBQ0UsOERBQUNOO1FBQUlDLE9BQU07UUFBS0MsUUFBTztRQUFLQyxTQUFRO1FBQVlDLE1BQUs7a0JBQ25ELDRFQUFDQztZQUFLQyxHQUFFOzs7Ozs7Ozs7OztJQUdaLDhDQUNFLDhEQUFDTjtRQUFJQyxPQUFNO1FBQUtDLFFBQU87UUFBS0MsU0FBUTtRQUFZQyxNQUFLO2tCQUNuRCw0RUFBQ0M7WUFBS0MsR0FBRTs7Ozs7Ozs7Ozs7SUFHWixzQ0FDRSw4REFBQ047UUFBSUMsT0FBTTtRQUFLQyxRQUFPO1FBQUtDLFNBQVE7UUFBWUMsTUFBSztrQkFDbkQsNEVBQUNDO1lBQUtDLEdBQUU7Ozs7Ozs7Ozs7O0lBR1osd0NBQ0UsOERBQUNOO1FBQUlDLE9BQU07UUFBS0MsUUFBTztRQUFLQyxTQUFRO1FBQVlDLE1BQUs7a0JBQ25ELDRFQUFDQztZQUFLQyxHQUFFOzs7Ozs7Ozs7OztBQUdkO0FBRWUsU0FBU0UsUUFBUSxFQUFFQyxhQUFhWixzRUFBc0IsRUFBRWEsV0FBVyxFQUFnQjtJQUNoRyxNQUFNQyxXQUFXZiw0REFBV0E7SUFDNUIsTUFBTSxDQUFDZ0Isb0JBQW9CQyxzQkFBc0IsR0FBR25CLCtDQUFRQSxDQUFjLElBQUlvQixJQUFJO1FBQUM7S0FBYztJQUVqRyxNQUFNQyxpQkFBaUIsQ0FBQ0M7UUFDdEIsTUFBTUMsY0FBYyxJQUFJSCxJQUFJRjtRQUM1QixJQUFJSyxZQUFZQyxHQUFHLENBQUNGLGFBQWE7WUFDL0JDLFlBQVlFLE1BQU0sQ0FBQ0g7UUFDckIsT0FBTztZQUNMQyxZQUFZRyxHQUFHLENBQUNKO1FBQ2xCO1FBQ0FILHNCQUFzQkk7SUFDeEI7SUFFQSxNQUFNSSxXQUFXLENBQUNDO1FBQ2hCLE9BQU9YLGFBQWFXO0lBQ3RCO0lBRUEsTUFBTUMsbUJBQW1CLENBQUNQO1FBQ3hCLE9BQU9MLFNBQVNhLFVBQVUsQ0FBQyxDQUFDLENBQUMsRUFBRVIsWUFBWTtJQUM3QztJQUVBLHFCQUNFLDhEQUFDUztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDL0Isa0RBQUlBO3dCQUFDMkIsTUFBSztrQ0FDVCw0RUFBQ007NEJBQUdGLFdBQVU7c0NBQWtDOzs7Ozs7Ozs7OztrQ0FJbEQsOERBQUNHO3dCQUFFSCxXQUFVO2tDQUE2Qjs7Ozs7O2tDQUcxQyw4REFBQ0M7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUMvQixrREFBSUE7NEJBQUMyQixNQUFLO3NDQUNULDRFQUFDSztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUMxQjt3Q0FBSTBCLFdBQVU7d0NBQVV2QixTQUFRO3dDQUFZQyxNQUFLO2tEQUNoRCw0RUFBQ0M7NENBQUtDLEdBQUU7Ozs7Ozs7Ozs7O2tEQUVWLDhEQUFDd0I7a0RBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT2QsOERBQUNIO2dCQUFJRCxXQUFVOzBCQUNaakIsV0FBV3NCLEdBQUcsQ0FBQyxDQUFDQztvQkFDZixNQUFNQyxhQUFhckIsbUJBQW1CTSxHQUFHLENBQUNjLFNBQVNFLEVBQUU7b0JBQ3JELE1BQU1DLGNBQWNaLGlCQUFpQlMsU0FBU0UsRUFBRTtvQkFFaEQscUJBQ0UsOERBQUNQO3dCQUFzQkQsV0FBVTs7MENBRS9CLDhEQUFDVTtnQ0FDQ0MsU0FBUyxJQUFNdEIsZUFBZWlCLFNBQVNFLEVBQUU7Z0NBQ3pDUixXQUFXLENBQUMscUdBQXFHLEVBQy9HUyxjQUNJLDhCQUNBLHVEQUNKOztrREFFRiw4REFBQ1I7d0NBQUlELFdBQVU7OzBEQUNiLDhEQUFDSTtnREFBS0osV0FBVTswREFDYjVCLGFBQWEsQ0FBQ2tDLFNBQVNFLEVBQUUsQ0FBK0I7Ozs7OzswREFFM0QsOERBQUNKOzBEQUFNRSxTQUFTTSxLQUFLOzs7Ozs7Ozs7Ozs7a0RBRXZCLDhEQUFDUjt3Q0FDQ0osV0FBVyxDQUFDLDZCQUE2QixFQUN2Q08sYUFBYSxjQUFjLFlBQzNCO2tEQUVGLDRFQUFDakM7NENBQUlHLFNBQVE7NENBQVlDLE1BQUs7c0RBQzVCLDRFQUFDQztnREFBS0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0QkFNYjJCLDRCQUNDLDhEQUFDTjtnQ0FBSUQsV0FBVTswQ0FDWk0sU0FBU08sVUFBVSxDQUFDUixHQUFHLENBQUMsQ0FBQ1M7b0NBQ3hCLE1BQU1DLGdCQUFnQixDQUFDLENBQUMsRUFBRVQsU0FBU0UsRUFBRSxDQUFDLENBQUMsRUFBRU0sVUFBVU4sRUFBRSxFQUFFO29DQUN2RCxNQUFNUSxvQkFBb0JyQixTQUFTb0I7b0NBRW5DLHFCQUNFLDhEQUFDOUMsa0RBQUlBO3dDQUFvQjJCLE1BQU1tQjtrREFDN0IsNEVBQUNkOzRDQUNDRCxXQUFXLENBQUMsNENBQTRDLEVBQ3REZ0Isb0JBQ0ksNkJBQ0EsdURBQ0o7c0RBRURGLFVBQVVHLElBQUk7Ozs7Ozt1Q0FSUkgsVUFBVU4sRUFBRTs7Ozs7Z0NBWTNCOzs7Ozs7O3VCQS9DSUYsU0FBU0UsRUFBRTs7Ozs7Z0JBb0R6Qjs7Ozs7Ozs7Ozs7O0FBSVIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYWhtb3VkbWV0d2FseS9Eb2N1bWVudHMvUHJvamVjdHMvRCBzeXN0ZW0vY29tcG9uZW50cy9TaWRlYmFyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCB7IHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IGRlc2lnblN5c3RlbUNhdGVnb3JpZXMgfSBmcm9tICdAL2xpYi9kZXNpZ24tc3lzdGVtJztcbmltcG9ydCB7IFNpZGViYXJQcm9wcyB9IGZyb20gJ0AvdHlwZXMvZGVzaWduLXN5c3RlbSc7XG5cbi8vIEljb25zIGZvciBjYXRlZ29yaWVzICh1c2luZyBzaW1wbGUgU1ZHIGljb25zKVxuY29uc3QgQ2F0ZWdvcnlJY29ucyA9IHtcbiAgdGh1bWJuYWlsOiAoXG4gICAgPHN2ZyB3aWR0aD1cIjE2XCIgaGVpZ2h0PVwiMTZcIiB2aWV3Qm94PVwiMCAwIDE2IDE2XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgPHBhdGggZD1cIk0yIDNhMSAxIDAgMDExLTFoMTBhMSAxIDAgMDExIDF2MTBhMSAxIDAgMDEtMSAxSDNhMSAxIDAgMDEtMS0xVjN6bTIgMXY4aDhWNEg0elwiLz5cbiAgICA8L3N2Zz5cbiAgKSxcbiAgZm91bmRhdGlvbnM6IChcbiAgICA8c3ZnIHdpZHRoPVwiMTZcIiBoZWlnaHQ9XCIxNlwiIHZpZXdCb3g9XCIwIDAgMTYgMTZcIiBmaWxsPVwiY3VycmVudENvbG9yXCI+XG4gICAgICA8cGF0aCBkPVwiTTggMWw3IDR2NmwtNyA0LTctNFY1bDctNHptMCAyTDMuNSA1LjUgOCA4bDQuNS0yLjVMOCAzem0tNSA0djRsNSAyLjVWOS41TDMgN3ptMTAgMEw4IDkuNVYxNGw1LTIuNVY3elwiLz5cbiAgICA8L3N2Zz5cbiAgKSxcbiAgJ3NoYXJlZC1jb21wb25lbnRzJzogKFxuICAgIDxzdmcgd2lkdGg9XCIxNlwiIGhlaWdodD1cIjE2XCIgdmlld0JveD1cIjAgMCAxNiAxNlwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIj5cbiAgICAgIDxwYXRoIGQ9XCJNNCAyYTIgMiAwIDAwLTIgMnY4YTIgMiAwIDAwMiAyaDhhMiAyIDAgMDAyLTJWNGEyIDIgMCAwMC0yLTJINHptMCAyaDh2OEg0VjR6XCIvPlxuICAgIDwvc3ZnPlxuICApLFxuICAnc2hhcmVkLWFzc2V0cyc6IChcbiAgICA8c3ZnIHdpZHRoPVwiMTZcIiBoZWlnaHQ9XCIxNlwiIHZpZXdCb3g9XCIwIDAgMTYgMTZcIiBmaWxsPVwiY3VycmVudENvbG9yXCI+XG4gICAgICA8cGF0aCBkPVwiTTggMWE3IDcgMCAxMDAgMTRBNyA3IDAgMDA4IDF6TTYgNmExIDEgMCAxMTIgMCAxIDEgMCAwMS0yIDB6bTEgM2ExIDEgMCAxMDAgMiAxIDEgMCAwMDAtMnpcIi8+XG4gICAgPC9zdmc+XG4gICksXG4gICdtYXJrZXRpbmctd2Vic2l0ZS1leGFtcGxlcyc6IChcbiAgICA8c3ZnIHdpZHRoPVwiMTZcIiBoZWlnaHQ9XCIxNlwiIHZpZXdCb3g9XCIwIDAgMTYgMTZcIiBmaWxsPVwiY3VycmVudENvbG9yXCI+XG4gICAgICA8cGF0aCBkPVwiTTEgMmExIDEgMCAwMTEtMWgxMmExIDEgMCAwMTEgMXYxMmExIDEgMCAwMS0xIDFIMmExIDEgMCAwMS0xLTFWMnptMiAxdjEwaDEwVjNIM3pcIi8+XG4gICAgPC9zdmc+XG4gICksXG4gICdtYXJrZXRpbmctd2Vic2l0ZS1jb21wb25lbnRzJzogKFxuICAgIDxzdmcgd2lkdGg9XCIxNlwiIGhlaWdodD1cIjE2XCIgdmlld0JveD1cIjAgMCAxNiAxNlwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIj5cbiAgICAgIDxwYXRoIGQ9XCJNMiAyaDEydjJIMlYyem0wIDRoMTJ2MkgyVjZ6bTAgNGg4djJIMnYtMnpcIi8+XG4gICAgPC9zdmc+XG4gICksXG4gICdhcHBsaWNhdGlvbi1leGFtcGxlcyc6IChcbiAgICA8c3ZnIHdpZHRoPVwiMTZcIiBoZWlnaHQ9XCIxNlwiIHZpZXdCb3g9XCIwIDAgMTYgMTZcIiBmaWxsPVwiY3VycmVudENvbG9yXCI+XG4gICAgICA8cGF0aCBkPVwiTTEgMWgxNHYxNEgxVjF6bTIgMnYxMGgxMFYzSDN6XCIvPlxuICAgIDwvc3ZnPlxuICApLFxuICAnYXBwbGljYXRpb24tY29tcG9uZW50cyc6IChcbiAgICA8c3ZnIHdpZHRoPVwiMTZcIiBoZWlnaHQ9XCIxNlwiIHZpZXdCb3g9XCIwIDAgMTYgMTZcIiBmaWxsPVwiY3VycmVudENvbG9yXCI+XG4gICAgICA8cGF0aCBkPVwiTTMgM2g0djRIM1Yzem02IDBoNHY0SDlWM3pNMyA5aDR2NEgzVjl6bTYgMGg0djRIOVY5elwiLz5cbiAgICA8L3N2Zz5cbiAgKSxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNpZGViYXIoeyBjYXRlZ29yaWVzID0gZGVzaWduU3lzdGVtQ2F0ZWdvcmllcywgY3VycmVudFBhdGggfTogU2lkZWJhclByb3BzKSB7XG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKTtcbiAgY29uc3QgW2V4cGFuZGVkQ2F0ZWdvcmllcywgc2V0RXhwYW5kZWRDYXRlZ29yaWVzXSA9IHVzZVN0YXRlPFNldDxzdHJpbmc+PihuZXcgU2V0KFsnZm91bmRhdGlvbnMnXSkpO1xuXG4gIGNvbnN0IHRvZ2dsZUNhdGVnb3J5ID0gKGNhdGVnb3J5SWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IG5ld0V4cGFuZGVkID0gbmV3IFNldChleHBhbmRlZENhdGVnb3JpZXMpO1xuICAgIGlmIChuZXdFeHBhbmRlZC5oYXMoY2F0ZWdvcnlJZCkpIHtcbiAgICAgIG5ld0V4cGFuZGVkLmRlbGV0ZShjYXRlZ29yeUlkKTtcbiAgICB9IGVsc2Uge1xuICAgICAgbmV3RXhwYW5kZWQuYWRkKGNhdGVnb3J5SWQpO1xuICAgIH1cbiAgICBzZXRFeHBhbmRlZENhdGVnb3JpZXMobmV3RXhwYW5kZWQpO1xuICB9O1xuXG4gIGNvbnN0IGlzQWN0aXZlID0gKGhyZWY6IHN0cmluZykgPT4ge1xuICAgIHJldHVybiBwYXRobmFtZSA9PT0gaHJlZjtcbiAgfTtcblxuICBjb25zdCBpc0NhdGVnb3J5QWN0aXZlID0gKGNhdGVnb3J5SWQ6IHN0cmluZykgPT4ge1xuICAgIHJldHVybiBwYXRobmFtZS5zdGFydHNXaXRoKGAvJHtjYXRlZ29yeUlkfWApO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPG5hdiBjbGFzc05hbWU9XCJmaXhlZCBsZWZ0LTAgdG9wLTAgei0xMCBoLXNjcmVlbiB3LVsyODBweF0gb3ZlcmZsb3cteS1hdXRvIGJvcmRlci1yIGJnLWdyYXktNTBcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci1iIHAtNlwiPlxuICAgICAgICA8TGluayBocmVmPVwiL1wiPlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICBEZXNpZ24gU3lzdGVtXG4gICAgICAgICAgPC9oMT5cbiAgICAgICAgPC9MaW5rPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgIENoYWtyYSBVSSBSU0MgTGlicmFyeVxuICAgICAgICA8L3A+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtM1wiPlxuICAgICAgICAgIDxMaW5rIGhyZWY9XCIvZmlnbWEtc3luY1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXNtIHRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTcwMFwiPlxuICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiB2aWV3Qm94PVwiMCAwIDE2IDE2XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNOCAxYTcgNyAwIDEwMCAxNEE3IDcgMCAwMDggMXpNNiA2YTEgMSAwIDExMiAwIDEgMSAwIDAxLTIgMHptMSAzYTEgMSAwIDEwMCAyIDEgMSAwIDAwMC0yelwiLz5cbiAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgIDxzcGFuPkZpZ21hIFN5bmM8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0xpbms+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBOYXZpZ2F0aW9uICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAge2NhdGVnb3JpZXMubWFwKChjYXRlZ29yeSkgPT4ge1xuICAgICAgICAgIGNvbnN0IGlzRXhwYW5kZWQgPSBleHBhbmRlZENhdGVnb3JpZXMuaGFzKGNhdGVnb3J5LmlkKTtcbiAgICAgICAgICBjb25zdCBpc0NhdEFjdGl2ZSA9IGlzQ2F0ZWdvcnlBY3RpdmUoY2F0ZWdvcnkuaWQpO1xuXG4gICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDxkaXYga2V5PXtjYXRlZ29yeS5pZH0gY2xhc3NOYW1lPVwibWItMlwiPlxuICAgICAgICAgICAgICB7LyogQ2F0ZWdvcnkgSGVhZGVyICovfVxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gdG9nZ2xlQ2F0ZWdvcnkoY2F0ZWdvcnkuaWQpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC0zIHRleHQtbGVmdCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgIGlzQ2F0QWN0aXZlXG4gICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyYXktMjAwIHRleHQtZ3JheS05MDAnXG4gICAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS02MDAgaG92ZXI6YmctZ3JheS0xMDAgaG92ZXI6dGV4dC1ncmF5LTkwMCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInctNCBoLTRcIj5cbiAgICAgICAgICAgICAgICAgICAge0NhdGVnb3J5SWNvbnNbY2F0ZWdvcnkuaWQgYXMga2V5b2YgdHlwZW9mIENhdGVnb3J5SWNvbnNdfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+e2NhdGVnb3J5LnRpdGxlfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8c3BhblxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy0zIGgtMyB0cmFuc2l0aW9uLXRyYW5zZm9ybSAke1xuICAgICAgICAgICAgICAgICAgICBpc0V4cGFuZGVkID8gJ3JvdGF0ZS05MCcgOiAncm90YXRlLTAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8c3ZnIHZpZXdCb3g9XCIwIDAgMTYgMTZcIiBmaWxsPVwiY3VycmVudENvbG9yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNNiA0bDQgNC00IDRWNHpcIi8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgIHsvKiBDYXRlZ29yeSBDb21wb25lbnRzICovfVxuICAgICAgICAgICAgICB7aXNFeHBhbmRlZCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC02IHB5LTJcIj5cbiAgICAgICAgICAgICAgICAgIHtjYXRlZ29yeS5jb21wb25lbnRzLm1hcCgoY29tcG9uZW50KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGNvbXBvbmVudEhyZWYgPSBgLyR7Y2F0ZWdvcnkuaWR9LyR7Y29tcG9uZW50LmlkfWA7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzQ29tcG9uZW50QWN0aXZlID0gaXNBY3RpdmUoY29tcG9uZW50SHJlZik7XG5cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICA8TGluayBrZXk9e2NvbXBvbmVudC5pZH0gaHJlZj17Y29tcG9uZW50SHJlZn0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGJsb2NrIHAtMiB0ZXh0LXNtIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0NvbXBvbmVudEFjdGl2ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS01MCB0ZXh0LWJsdWUtNjAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTYwMCBob3ZlcjpiZy1ncmF5LTEwMCBob3Zlcjp0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbXBvbmVudC5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApO1xuICAgICAgICB9KX1cbiAgICAgIDwvZGl2PlxuICAgIDwvbmF2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiTGluayIsInVzZVBhdGhuYW1lIiwiZGVzaWduU3lzdGVtQ2F0ZWdvcmllcyIsIkNhdGVnb3J5SWNvbnMiLCJ0aHVtYm5haWwiLCJzdmciLCJ3aWR0aCIsImhlaWdodCIsInZpZXdCb3giLCJmaWxsIiwicGF0aCIsImQiLCJmb3VuZGF0aW9ucyIsIlNpZGViYXIiLCJjYXRlZ29yaWVzIiwiY3VycmVudFBhdGgiLCJwYXRobmFtZSIsImV4cGFuZGVkQ2F0ZWdvcmllcyIsInNldEV4cGFuZGVkQ2F0ZWdvcmllcyIsIlNldCIsInRvZ2dsZUNhdGVnb3J5IiwiY2F0ZWdvcnlJZCIsIm5ld0V4cGFuZGVkIiwiaGFzIiwiZGVsZXRlIiwiYWRkIiwiaXNBY3RpdmUiLCJocmVmIiwiaXNDYXRlZ29yeUFjdGl2ZSIsInN0YXJ0c1dpdGgiLCJuYXYiLCJjbGFzc05hbWUiLCJkaXYiLCJoMSIsInAiLCJzcGFuIiwibWFwIiwiY2F0ZWdvcnkiLCJpc0V4cGFuZGVkIiwiaWQiLCJpc0NhdEFjdGl2ZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJ0aXRsZSIsImNvbXBvbmVudHMiLCJjb21wb25lbnQiLCJjb21wb25lbnRIcmVmIiwiaXNDb21wb25lbnRBY3RpdmUiLCJuYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/design-system.ts":
/*!******************************!*\
  !*** ./lib/design-system.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   designSystemCategories: () => (/* binding */ designSystemCategories),\n/* harmony export */   getAllComponents: () => (/* binding */ getAllComponents),\n/* harmony export */   getCategoryById: () => (/* binding */ getCategoryById),\n/* harmony export */   getComponentById: () => (/* binding */ getComponentById),\n/* harmony export */   getComponentsByCategory: () => (/* binding */ getComponentsByCategory)\n/* harmony export */ });\n// Design System Configuration based on Figma file\nconst designSystemCategories = [\n    {\n        id: 'thumbnail',\n        title: 'Thumbnail',\n        description: 'Overview and preview of the design system',\n        components: [\n            {\n                id: 'overview',\n                name: 'Design System Overview',\n                category: 'thumbnail',\n                description: 'Complete overview of the design system components and guidelines',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=2210-441096',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {}\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'foundations',\n        title: 'Foundations',\n        description: 'Core design tokens, colors, typography, and spacing',\n        components: [\n            {\n                id: 'colors',\n                name: 'Color System',\n                category: 'foundations',\n                description: 'Primary, secondary, and semantic color palettes with accessibility guidelines',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=colors',\n                variants: [\n                    {\n                        name: 'Primary Palette',\n                        props: {\n                            palette: 'primary'\n                        }\n                    },\n                    {\n                        name: 'Secondary Palette',\n                        props: {\n                            palette: 'secondary'\n                        }\n                    },\n                    {\n                        name: 'Semantic Colors',\n                        props: {\n                            palette: 'semantic'\n                        }\n                    },\n                    {\n                        name: 'Neutral Colors',\n                        props: {\n                            palette: 'neutral'\n                        }\n                    },\n                    {\n                        name: 'Brand Colors',\n                        props: {\n                            palette: 'brand'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'primary-50',\n                        value: '#eff6ff',\n                        type: 'color',\n                        description: 'Primary color lightest shade'\n                    },\n                    {\n                        name: 'primary-500',\n                        value: '#3b82f6',\n                        type: 'color',\n                        description: 'Primary color base'\n                    },\n                    {\n                        name: 'primary-900',\n                        value: '#1e3a8a',\n                        type: 'color',\n                        description: 'Primary color darkest shade'\n                    }\n                ]\n            },\n            {\n                id: 'typography',\n                name: 'Typography Scale',\n                category: 'foundations',\n                description: 'Font families, sizes, weights, and line heights for consistent text hierarchy',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=typography',\n                variants: [\n                    {\n                        name: 'Display Text',\n                        props: {\n                            category: 'display'\n                        }\n                    },\n                    {\n                        name: 'Headings',\n                        props: {\n                            category: 'headings'\n                        }\n                    },\n                    {\n                        name: 'Body Text',\n                        props: {\n                            category: 'body'\n                        }\n                    },\n                    {\n                        name: 'Labels & Captions',\n                        props: {\n                            category: 'labels'\n                        }\n                    },\n                    {\n                        name: 'Code & Monospace',\n                        props: {\n                            category: 'code'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'font-size-xs',\n                        value: '12px',\n                        type: 'typography',\n                        description: 'Extra small text size'\n                    },\n                    {\n                        name: 'font-size-base',\n                        value: '16px',\n                        type: 'typography',\n                        description: 'Base text size'\n                    },\n                    {\n                        name: 'font-size-2xl',\n                        value: '24px',\n                        type: 'typography',\n                        description: 'Large heading size'\n                    }\n                ]\n            },\n            {\n                id: 'spacing',\n                name: 'Spacing System',\n                category: 'foundations',\n                description: '8px grid system for consistent spacing and layout',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=spacing',\n                variants: [\n                    {\n                        name: 'Base Scale',\n                        props: {\n                            scale: 'base'\n                        }\n                    },\n                    {\n                        name: 'Component Spacing',\n                        props: {\n                            scale: 'component'\n                        }\n                    },\n                    {\n                        name: 'Layout Spacing',\n                        props: {\n                            scale: 'layout'\n                        }\n                    },\n                    {\n                        name: 'Responsive Spacing',\n                        props: {\n                            scale: 'responsive'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'space-1',\n                        value: '4px',\n                        type: 'spacing',\n                        description: 'Smallest spacing unit'\n                    },\n                    {\n                        name: 'space-4',\n                        value: '16px',\n                        type: 'spacing',\n                        description: 'Base spacing unit'\n                    },\n                    {\n                        name: 'space-8',\n                        value: '32px',\n                        type: 'spacing',\n                        description: 'Large spacing unit'\n                    }\n                ]\n            },\n            {\n                id: 'elevation',\n                name: 'Elevation & Shadows',\n                category: 'foundations',\n                description: 'Shadow system for creating depth and visual hierarchy',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=elevation',\n                variants: [\n                    {\n                        name: 'Card Shadows',\n                        props: {\n                            type: 'card'\n                        }\n                    },\n                    {\n                        name: 'Modal Shadows',\n                        props: {\n                            type: 'modal'\n                        }\n                    },\n                    {\n                        name: 'Focus Rings',\n                        props: {\n                            type: 'focus'\n                        }\n                    },\n                    {\n                        name: 'Dropdown Shadows',\n                        props: {\n                            type: 'dropdown'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'shadow-sm',\n                        value: '0 1px 2px 0 rgb(0 0 0 / 0.05)',\n                        type: 'shadow',\n                        description: 'Small shadow'\n                    },\n                    {\n                        name: 'shadow-md',\n                        value: '0 4px 6px -1px rgb(0 0 0 / 0.1)',\n                        type: 'shadow',\n                        description: 'Medium shadow'\n                    },\n                    {\n                        name: 'shadow-lg',\n                        value: '0 10px 15px -3px rgb(0 0 0 / 0.1)',\n                        type: 'shadow',\n                        description: 'Large shadow'\n                    }\n                ]\n            },\n            {\n                id: 'grid',\n                name: 'Grid System',\n                category: 'foundations',\n                description: 'Responsive grid system and layout guidelines',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=grid',\n                variants: [\n                    {\n                        name: '12 Column Grid',\n                        props: {\n                            columns: 12\n                        }\n                    },\n                    {\n                        name: 'Responsive Breakpoints',\n                        props: {\n                            type: 'breakpoints'\n                        }\n                    },\n                    {\n                        name: 'Container Sizes',\n                        props: {\n                            type: 'containers'\n                        }\n                    },\n                    {\n                        name: 'Gutters & Margins',\n                        props: {\n                            type: 'gutters'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'shared-components',\n        title: 'Shared Components',\n        description: 'Reusable UI components used across applications',\n        components: [\n            {\n                id: 'button',\n                name: 'Button',\n                category: 'shared-components',\n                description: 'Primary interactive element with multiple variants and states',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=button',\n                variants: [\n                    {\n                        name: 'Primary',\n                        props: {\n                            variant: 'primary',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Secondary',\n                        props: {\n                            variant: 'secondary',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Outline',\n                        props: {\n                            variant: 'outline',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Ghost',\n                        props: {\n                            variant: 'ghost',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Destructive',\n                        props: {\n                            variant: 'destructive',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Small',\n                        props: {\n                            variant: 'primary',\n                            size: 'sm'\n                        }\n                    },\n                    {\n                        name: 'Large',\n                        props: {\n                            variant: 'primary',\n                            size: 'lg'\n                        }\n                    },\n                    {\n                        name: 'Icon Only',\n                        props: {\n                            variant: 'primary',\n                            iconOnly: true\n                        }\n                    },\n                    {\n                        name: 'Loading',\n                        props: {\n                            variant: 'primary',\n                            loading: true\n                        }\n                    },\n                    {\n                        name: 'Disabled',\n                        props: {\n                            variant: 'primary',\n                            disabled: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'input',\n                name: 'Input Field',\n                category: 'shared-components',\n                description: 'Text input with labels, validation, and helper text',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=input',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {\n                            placeholder: 'Enter text...'\n                        }\n                    },\n                    {\n                        name: 'With Label',\n                        props: {\n                            label: 'Email Address',\n                            placeholder: 'Enter your email'\n                        }\n                    },\n                    {\n                        name: 'Required',\n                        props: {\n                            label: 'Password',\n                            required: true,\n                            type: 'password'\n                        }\n                    },\n                    {\n                        name: 'Error State',\n                        props: {\n                            label: 'Username',\n                            error: 'Username is already taken'\n                        }\n                    },\n                    {\n                        name: 'Success State',\n                        props: {\n                            label: 'Email',\n                            success: 'Email is available'\n                        }\n                    },\n                    {\n                        name: 'Disabled',\n                        props: {\n                            label: 'Disabled Field',\n                            disabled: true\n                        }\n                    },\n                    {\n                        name: 'With Icon',\n                        props: {\n                            label: 'Search',\n                            icon: 'search',\n                            placeholder: 'Search...'\n                        }\n                    },\n                    {\n                        name: 'Textarea',\n                        props: {\n                            label: 'Message',\n                            type: 'textarea',\n                            rows: 4\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'card',\n                name: 'Card',\n                category: 'shared-components',\n                description: 'Flexible container for grouping related content',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=card',\n                variants: [\n                    {\n                        name: 'Basic Card',\n                        props: {\n                            variant: 'default'\n                        }\n                    },\n                    {\n                        name: 'Elevated Card',\n                        props: {\n                            variant: 'elevated'\n                        }\n                    },\n                    {\n                        name: 'Outlined Card',\n                        props: {\n                            variant: 'outlined'\n                        }\n                    },\n                    {\n                        name: 'Interactive Card',\n                        props: {\n                            variant: 'interactive',\n                            clickable: true\n                        }\n                    },\n                    {\n                        name: 'Product Card',\n                        props: {\n                            variant: 'product',\n                            image: true,\n                            badge: true\n                        }\n                    },\n                    {\n                        name: 'Profile Card',\n                        props: {\n                            variant: 'profile',\n                            avatar: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'badge',\n                name: 'Badge',\n                category: 'shared-components',\n                description: 'Small status indicators and labels',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=badge',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {\n                            variant: 'default'\n                        }\n                    },\n                    {\n                        name: 'Primary',\n                        props: {\n                            variant: 'primary'\n                        }\n                    },\n                    {\n                        name: 'Success',\n                        props: {\n                            variant: 'success'\n                        }\n                    },\n                    {\n                        name: 'Warning',\n                        props: {\n                            variant: 'warning'\n                        }\n                    },\n                    {\n                        name: 'Error',\n                        props: {\n                            variant: 'error'\n                        }\n                    },\n                    {\n                        name: 'Outline',\n                        props: {\n                            variant: 'outline'\n                        }\n                    },\n                    {\n                        name: 'Dot Indicator',\n                        props: {\n                            variant: 'dot'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'avatar',\n                name: 'Avatar',\n                category: 'shared-components',\n                description: 'User profile pictures and initials',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=avatar',\n                variants: [\n                    {\n                        name: 'Image Avatar',\n                        props: {\n                            type: 'image',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Initials',\n                        props: {\n                            type: 'initials',\n                            initials: 'JD',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Icon Avatar',\n                        props: {\n                            type: 'icon',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Small',\n                        props: {\n                            type: 'image',\n                            size: 'sm'\n                        }\n                    },\n                    {\n                        name: 'Large',\n                        props: {\n                            type: 'image',\n                            size: 'lg'\n                        }\n                    },\n                    {\n                        name: 'With Status',\n                        props: {\n                            type: 'image',\n                            status: 'online',\n                            size: 'md'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'dropdown',\n                name: 'Dropdown Menu',\n                category: 'shared-components',\n                description: 'Contextual menus and select components',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=dropdown',\n                variants: [\n                    {\n                        name: 'Basic Dropdown',\n                        props: {\n                            variant: 'basic'\n                        }\n                    },\n                    {\n                        name: 'With Icons',\n                        props: {\n                            variant: 'icons'\n                        }\n                    },\n                    {\n                        name: 'With Dividers',\n                        props: {\n                            variant: 'dividers'\n                        }\n                    },\n                    {\n                        name: 'Multi-select',\n                        props: {\n                            variant: 'multiselect'\n                        }\n                    },\n                    {\n                        name: 'Searchable',\n                        props: {\n                            variant: 'searchable'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'shared-assets',\n        title: 'Shared Assets',\n        description: 'Icons, illustrations, and other visual assets',\n        components: [\n            {\n                id: 'icons',\n                name: 'Icons',\n                category: 'shared-assets',\n                description: 'Icon library and usage guidelines',\n                variants: [\n                    {\n                        name: 'Interface',\n                        props: {\n                            category: 'interface'\n                        }\n                    },\n                    {\n                        name: 'Actions',\n                        props: {\n                            category: 'actions'\n                        }\n                    },\n                    {\n                        name: 'Status',\n                        props: {\n                            category: 'status'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'illustrations',\n                name: 'Illustrations',\n                category: 'shared-assets',\n                description: 'Illustration library for empty states and onboarding',\n                variants: [\n                    {\n                        name: 'Empty States',\n                        props: {\n                            type: 'empty'\n                        }\n                    },\n                    {\n                        name: 'Onboarding',\n                        props: {\n                            type: 'onboarding'\n                        }\n                    },\n                    {\n                        name: 'Error States',\n                        props: {\n                            type: 'error'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'marketing-website-examples',\n        title: 'Marketing Website Examples',\n        description: 'Complete page examples for marketing websites',\n        components: [\n            {\n                id: 'landing-page',\n                name: 'Landing Page',\n                category: 'marketing-website-examples',\n                description: 'Complete landing page layout with hero, features, and CTA',\n                variants: [\n                    {\n                        name: 'SaaS',\n                        props: {\n                            type: 'saas'\n                        }\n                    },\n                    {\n                        name: 'Product',\n                        props: {\n                            type: 'product'\n                        }\n                    },\n                    {\n                        name: 'Agency',\n                        props: {\n                            type: 'agency'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'pricing-page',\n                name: 'Pricing Page',\n                category: 'marketing-website-examples',\n                description: 'Pricing page with plans and feature comparison',\n                variants: [\n                    {\n                        name: 'Simple',\n                        props: {\n                            layout: 'simple'\n                        }\n                    },\n                    {\n                        name: 'Comparison',\n                        props: {\n                            layout: 'comparison'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'marketing-website-components',\n        title: 'Marketing Website Components',\n        description: 'Specialized components for marketing websites',\n        components: [\n            {\n                id: 'hero-section',\n                name: 'Hero Section',\n                category: 'marketing-website-components',\n                description: 'Hero section with headline, description, and CTA',\n                variants: [\n                    {\n                        name: 'Centered',\n                        props: {\n                            alignment: 'center'\n                        }\n                    },\n                    {\n                        name: 'Left Aligned',\n                        props: {\n                            alignment: 'left'\n                        }\n                    },\n                    {\n                        name: 'With Image',\n                        props: {\n                            hasImage: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'feature-grid',\n                name: 'Feature Grid',\n                category: 'marketing-website-components',\n                description: 'Grid layout for showcasing product features',\n                variants: [\n                    {\n                        name: '3 Column',\n                        props: {\n                            columns: 3\n                        }\n                    },\n                    {\n                        name: '4 Column',\n                        props: {\n                            columns: 4\n                        }\n                    },\n                    {\n                        name: 'With Icons',\n                        props: {\n                            hasIcons: true\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'application-examples',\n        title: 'Application Examples',\n        description: 'Complete application page layouts and flows',\n        components: [\n            {\n                id: 'dashboard',\n                name: 'Dashboard',\n                category: 'application-examples',\n                description: 'Complete dashboard layout with sidebar and content area',\n                variants: [\n                    {\n                        name: 'Analytics',\n                        props: {\n                            type: 'analytics'\n                        }\n                    },\n                    {\n                        name: 'CRM',\n                        props: {\n                            type: 'crm'\n                        }\n                    },\n                    {\n                        name: 'E-commerce',\n                        props: {\n                            type: 'ecommerce'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'settings-page',\n                name: 'Settings Page',\n                category: 'application-examples',\n                description: 'Settings page with tabs and form sections',\n                variants: [\n                    {\n                        name: 'Profile',\n                        props: {\n                            section: 'profile'\n                        }\n                    },\n                    {\n                        name: 'Security',\n                        props: {\n                            section: 'security'\n                        }\n                    },\n                    {\n                        name: 'Billing',\n                        props: {\n                            section: 'billing'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'application-components',\n        title: 'Application Components',\n        description: 'Complex components for application interfaces',\n        components: [\n            {\n                id: 'data-table',\n                name: 'Data Table',\n                category: 'application-components',\n                description: 'Advanced data table with sorting, filtering, and pagination',\n                variants: [\n                    {\n                        name: 'Basic',\n                        props: {\n                            features: [\n                                'sorting'\n                            ]\n                        }\n                    },\n                    {\n                        name: 'Advanced',\n                        props: {\n                            features: [\n                                'sorting',\n                                'filtering',\n                                'pagination'\n                            ]\n                        }\n                    },\n                    {\n                        name: 'Selectable',\n                        props: {\n                            selectable: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'sidebar-navigation',\n                name: 'Sidebar Navigation',\n                category: 'application-components',\n                description: 'Collapsible sidebar navigation for applications',\n                variants: [\n                    {\n                        name: 'Expanded',\n                        props: {\n                            collapsed: false\n                        }\n                    },\n                    {\n                        name: 'Collapsed',\n                        props: {\n                            collapsed: true\n                        }\n                    },\n                    {\n                        name: 'With Submenu',\n                        props: {\n                            hasSubmenu: true\n                        }\n                    }\n                ]\n            }\n        ]\n    }\n];\n// Utility functions\nfunction getCategoryById(id) {\n    return designSystemCategories.find((category)=>category.id === id);\n}\nfunction getComponentById(componentId) {\n    for (const category of designSystemCategories){\n        const component = category.components.find((comp)=>comp.id === componentId);\n        if (component) return component;\n    }\n    return undefined;\n}\nfunction getAllComponents() {\n    return designSystemCategories.flatMap((category)=>category.components);\n}\nfunction getComponentsByCategory(categoryId) {\n    const category = getCategoryById(categoryId);\n    return category?.components || [];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/design-system.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Layout.tsx */ \"(ssr)/./components/Layout.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFobW91ZG1ldHdhbHklMkZEb2N1bWVudHMlMkZQcm9qZWN0cyUyRkQlMjBzeXN0ZW0lMkZjb21wb25lbnRzJTJGTGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZtYWhtb3VkbWV0d2FseSUyRkRvY3VtZW50cyUyRlByb2plY3RzJTJGRCUyMHN5c3RlbSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGYXBwLWRpciUyRmxpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEpBQXlJO0FBQ3pJO0FBQ0EsZ05BQThLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL21haG1vdWRtZXR3YWx5L0RvY3VtZW50cy9Qcm9qZWN0cy9EIHN5c3RlbS9jb21wb25lbnRzL0xheW91dC50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIl9fZXNNb2R1bGVcIixcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMvbWFobW91ZG1ldHdhbHkvRG9jdW1lbnRzL1Byb2plY3RzL0Qgc3lzdGVtL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2FwcC1kaXIvbGluay5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();