/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e3aa7206e54a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYWhtb3VkbWV0d2FseS9Eb2N1bWVudHMvUHJvamVjdHMvRCBzeXN0ZW0vYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZTNhYTcyMDZlNTRhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: 'Design System - Chakra UI RSC Library',\n    description: 'A comprehensive design system built with Next.js 15, React 19, and Chakra UI 3.3',\n    keywords: [\n        'design system',\n        'chakra ui',\n        'react',\n        'next.js',\n        'figma',\n        'components'\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/app/layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/app/layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDdUI7QUFFaEIsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO1FBQUM7UUFBaUI7UUFBYTtRQUFTO1FBQVc7UUFBUztLQUFhO0FBQ3JGLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyx3QkFBd0I7a0JBQ3RDLDRFQUFDQztzQkFDRUo7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYWhtb3VkbWV0d2FseS9Eb2N1bWVudHMvUHJvamVjdHMvRCBzeXN0ZW0vYXBwL2xheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJztcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdEZXNpZ24gU3lzdGVtIC0gQ2hha3JhIFVJIFJTQyBMaWJyYXJ5JyxcbiAgZGVzY3JpcHRpb246ICdBIGNvbXByZWhlbnNpdmUgZGVzaWduIHN5c3RlbSBidWlsdCB3aXRoIE5leHQuanMgMTUsIFJlYWN0IDE5LCBhbmQgQ2hha3JhIFVJIDMuMycsXG4gIGtleXdvcmRzOiBbJ2Rlc2lnbiBzeXN0ZW0nLCAnY2hha3JhIHVpJywgJ3JlYWN0JywgJ25leHQuanMnLCAnZmlnbWEnLCAnY29tcG9uZW50cyddLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cbiAgICAgIDxib2R5PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"(rsc)/./components/Layout.tsx\");\n/* harmony import */ var _lib_design_system__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/design-system */ \"(rsc)/./lib/design-system.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction HomePage() {\n    const totalComponents = _lib_design_system__WEBPACK_IMPORTED_MODULE_2__.designSystemCategories.reduce((total, category)=>total + category.components.length, 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Design System\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto mb-6\",\n                            children: \"A comprehensive design system built with Next.js 15, React 19, and Chakra UI 3.3. Featuring Figma integration and React Server Components.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"badge badge-blue px-3 py-1\",\n                                    children: \"Next.js 15\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"badge badge-green px-3 py-1\",\n                                    children: \"React 19\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"badge badge-purple px-3 py-1\",\n                                    children: \"Chakra UI 3.3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"badge badge-orange px-3 py-1\",\n                                    children: \"Figma Integration\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card text-center py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-blue-600\",\n                                    children: _lib_design_system__WEBPACK_IMPORTED_MODULE_2__.designSystemCategories.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Categories\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card text-center py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-blue-600\",\n                                    children: totalComponents\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Components\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card text-center py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-blue-600\",\n                                    children: \"RSC\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Server Components\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-6\",\n                            children: \"Categories\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: _lib_design_system__WEBPACK_IMPORTED_MODULE_2__.designSystemCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: `/${category.id}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card h-full cursor-pointer transition hover:shadow-lg hover:translate-y-[-2px]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-blue-50 rounded text-blue-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5\",\n                                                                viewBox: \"0 0 16 16\",\n                                                                fill: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M4 2a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h8v8H4V4z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                                    lineNumber: 74,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                                lineNumber: 73,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                            lineNumber: 72,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"badge badge-gray\",\n                                                            children: [\n                                                                category.components.length,\n                                                                \" components\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                            lineNumber: 77,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                            children: category.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: category.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this)\n                                }, category.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-6\",\n                            children: \"Getting Started\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                            children: \"Explore Components\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Browse through our comprehensive collection of components organized by category. Each component includes multiple variants and usage examples.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/foundations\",\n                                            className: \"text-blue-600 font-medium hover:text-blue-700\",\n                                            children: \"Start with Foundations →\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                            children: \"Figma Integration\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Our design system is connected to Figma, allowing you to sync design tokens and convert designs into React Server Components.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/shared-components\",\n                                            className: \"text-blue-600 font-medium hover:text-blue-700\",\n                                            children: \"View Components →\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./lib/design-system.ts":
/*!******************************!*\
  !*** ./lib/design-system.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   designSystemCategories: () => (/* binding */ designSystemCategories),\n/* harmony export */   getAllComponents: () => (/* binding */ getAllComponents),\n/* harmony export */   getCategoryById: () => (/* binding */ getCategoryById),\n/* harmony export */   getComponentById: () => (/* binding */ getComponentById),\n/* harmony export */   getComponentsByCategory: () => (/* binding */ getComponentsByCategory)\n/* harmony export */ });\n// Design System Configuration\nconst designSystemCategories = [\n    {\n        id: 'thumbnail',\n        title: 'Thumbnail',\n        description: 'Overview and preview of the design system',\n        components: [\n            {\n                id: 'overview',\n                name: 'Design System Overview',\n                category: 'thumbnail',\n                description: 'Complete overview of the design system components and guidelines',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {}\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'foundations',\n        title: 'Foundations',\n        description: 'Core design tokens, colors, typography, and spacing',\n        components: [\n            {\n                id: 'colors',\n                name: 'Colors',\n                category: 'foundations',\n                description: 'Color palette and semantic color tokens',\n                variants: [\n                    {\n                        name: 'Primary',\n                        props: {\n                            colorScheme: 'blue'\n                        }\n                    },\n                    {\n                        name: 'Secondary',\n                        props: {\n                            colorScheme: 'gray'\n                        }\n                    },\n                    {\n                        name: 'Success',\n                        props: {\n                            colorScheme: 'green'\n                        }\n                    },\n                    {\n                        name: 'Warning',\n                        props: {\n                            colorScheme: 'orange'\n                        }\n                    },\n                    {\n                        name: 'Error',\n                        props: {\n                            colorScheme: 'red'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'typography',\n                name: 'Typography',\n                category: 'foundations',\n                description: 'Font families, sizes, weights, and text styles',\n                variants: [\n                    {\n                        name: 'Headings',\n                        props: {\n                            type: 'headings'\n                        }\n                    },\n                    {\n                        name: 'Body Text',\n                        props: {\n                            type: 'body'\n                        }\n                    },\n                    {\n                        name: 'Captions',\n                        props: {\n                            type: 'captions'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'spacing',\n                name: 'Spacing',\n                category: 'foundations',\n                description: 'Spacing scale and layout tokens',\n                variants: [\n                    {\n                        name: 'Scale',\n                        props: {\n                            type: 'scale'\n                        }\n                    },\n                    {\n                        name: 'Layout',\n                        props: {\n                            type: 'layout'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'shadows',\n                name: 'Shadows',\n                category: 'foundations',\n                description: 'Shadow tokens for depth and elevation',\n                variants: [\n                    {\n                        name: 'Elevation',\n                        props: {\n                            type: 'elevation'\n                        }\n                    },\n                    {\n                        name: 'Focus',\n                        props: {\n                            type: 'focus'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'shared-components',\n        title: 'Shared Components',\n        description: 'Reusable UI components used across applications',\n        components: [\n            {\n                id: 'button',\n                name: 'Button',\n                category: 'shared-components',\n                description: 'Interactive button component with multiple variants',\n                variants: [\n                    {\n                        name: 'Primary',\n                        props: {\n                            variant: 'solid',\n                            colorScheme: 'blue'\n                        }\n                    },\n                    {\n                        name: 'Secondary',\n                        props: {\n                            variant: 'outline',\n                            colorScheme: 'blue'\n                        }\n                    },\n                    {\n                        name: 'Ghost',\n                        props: {\n                            variant: 'ghost',\n                            colorScheme: 'blue'\n                        }\n                    },\n                    {\n                        name: 'Link',\n                        props: {\n                            variant: 'plain',\n                            colorScheme: 'blue'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'input',\n                name: 'Input',\n                category: 'shared-components',\n                description: 'Text input component with validation states',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {\n                            placeholder: 'Enter text...'\n                        }\n                    },\n                    {\n                        name: 'Error',\n                        props: {\n                            invalid: true,\n                            placeholder: 'Invalid input'\n                        }\n                    },\n                    {\n                        name: 'Disabled',\n                        props: {\n                            disabled: true,\n                            placeholder: 'Disabled input'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'card',\n                name: 'Card',\n                category: 'shared-components',\n                description: 'Container component for grouping related content',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {}\n                    },\n                    {\n                        name: 'Elevated',\n                        props: {\n                            variant: 'elevated'\n                        }\n                    },\n                    {\n                        name: 'Outlined',\n                        props: {\n                            variant: 'outline'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'shared-assets',\n        title: 'Shared Assets',\n        description: 'Icons, illustrations, and other visual assets',\n        components: [\n            {\n                id: 'icons',\n                name: 'Icons',\n                category: 'shared-assets',\n                description: 'Icon library and usage guidelines',\n                variants: [\n                    {\n                        name: 'Interface',\n                        props: {\n                            category: 'interface'\n                        }\n                    },\n                    {\n                        name: 'Actions',\n                        props: {\n                            category: 'actions'\n                        }\n                    },\n                    {\n                        name: 'Status',\n                        props: {\n                            category: 'status'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'illustrations',\n                name: 'Illustrations',\n                category: 'shared-assets',\n                description: 'Illustration library for empty states and onboarding',\n                variants: [\n                    {\n                        name: 'Empty States',\n                        props: {\n                            type: 'empty'\n                        }\n                    },\n                    {\n                        name: 'Onboarding',\n                        props: {\n                            type: 'onboarding'\n                        }\n                    },\n                    {\n                        name: 'Error States',\n                        props: {\n                            type: 'error'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'marketing-website-examples',\n        title: 'Marketing Website Examples',\n        description: 'Complete page examples for marketing websites',\n        components: [\n            {\n                id: 'landing-page',\n                name: 'Landing Page',\n                category: 'marketing-website-examples',\n                description: 'Complete landing page layout with hero, features, and CTA',\n                variants: [\n                    {\n                        name: 'SaaS',\n                        props: {\n                            type: 'saas'\n                        }\n                    },\n                    {\n                        name: 'Product',\n                        props: {\n                            type: 'product'\n                        }\n                    },\n                    {\n                        name: 'Agency',\n                        props: {\n                            type: 'agency'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'pricing-page',\n                name: 'Pricing Page',\n                category: 'marketing-website-examples',\n                description: 'Pricing page with plans and feature comparison',\n                variants: [\n                    {\n                        name: 'Simple',\n                        props: {\n                            layout: 'simple'\n                        }\n                    },\n                    {\n                        name: 'Comparison',\n                        props: {\n                            layout: 'comparison'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'marketing-website-components',\n        title: 'Marketing Website Components',\n        description: 'Specialized components for marketing websites',\n        components: [\n            {\n                id: 'hero-section',\n                name: 'Hero Section',\n                category: 'marketing-website-components',\n                description: 'Hero section with headline, description, and CTA',\n                variants: [\n                    {\n                        name: 'Centered',\n                        props: {\n                            alignment: 'center'\n                        }\n                    },\n                    {\n                        name: 'Left Aligned',\n                        props: {\n                            alignment: 'left'\n                        }\n                    },\n                    {\n                        name: 'With Image',\n                        props: {\n                            hasImage: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'feature-grid',\n                name: 'Feature Grid',\n                category: 'marketing-website-components',\n                description: 'Grid layout for showcasing product features',\n                variants: [\n                    {\n                        name: '3 Column',\n                        props: {\n                            columns: 3\n                        }\n                    },\n                    {\n                        name: '4 Column',\n                        props: {\n                            columns: 4\n                        }\n                    },\n                    {\n                        name: 'With Icons',\n                        props: {\n                            hasIcons: true\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'application-examples',\n        title: 'Application Examples',\n        description: 'Complete application page layouts and flows',\n        components: [\n            {\n                id: 'dashboard',\n                name: 'Dashboard',\n                category: 'application-examples',\n                description: 'Complete dashboard layout with sidebar and content area',\n                variants: [\n                    {\n                        name: 'Analytics',\n                        props: {\n                            type: 'analytics'\n                        }\n                    },\n                    {\n                        name: 'CRM',\n                        props: {\n                            type: 'crm'\n                        }\n                    },\n                    {\n                        name: 'E-commerce',\n                        props: {\n                            type: 'ecommerce'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'settings-page',\n                name: 'Settings Page',\n                category: 'application-examples',\n                description: 'Settings page with tabs and form sections',\n                variants: [\n                    {\n                        name: 'Profile',\n                        props: {\n                            section: 'profile'\n                        }\n                    },\n                    {\n                        name: 'Security',\n                        props: {\n                            section: 'security'\n                        }\n                    },\n                    {\n                        name: 'Billing',\n                        props: {\n                            section: 'billing'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'application-components',\n        title: 'Application Components',\n        description: 'Complex components for application interfaces',\n        components: [\n            {\n                id: 'data-table',\n                name: 'Data Table',\n                category: 'application-components',\n                description: 'Advanced data table with sorting, filtering, and pagination',\n                variants: [\n                    {\n                        name: 'Basic',\n                        props: {\n                            features: [\n                                'sorting'\n                            ]\n                        }\n                    },\n                    {\n                        name: 'Advanced',\n                        props: {\n                            features: [\n                                'sorting',\n                                'filtering',\n                                'pagination'\n                            ]\n                        }\n                    },\n                    {\n                        name: 'Selectable',\n                        props: {\n                            selectable: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'sidebar-navigation',\n                name: 'Sidebar Navigation',\n                category: 'application-components',\n                description: 'Collapsible sidebar navigation for applications',\n                variants: [\n                    {\n                        name: 'Expanded',\n                        props: {\n                            collapsed: false\n                        }\n                    },\n                    {\n                        name: 'Collapsed',\n                        props: {\n                            collapsed: true\n                        }\n                    },\n                    {\n                        name: 'With Submenu',\n                        props: {\n                            hasSubmenu: true\n                        }\n                    }\n                ]\n            }\n        ]\n    }\n];\n// Utility functions\nfunction getCategoryById(id) {\n    return designSystemCategories.find((category)=>category.id === id);\n}\nfunction getComponentById(componentId) {\n    for (const category of designSystemCategories){\n        const component = category.components.find((comp)=>comp.id === componentId);\n        if (component) return component;\n    }\n    return undefined;\n}\nfunction getAllComponents() {\n    return designSystemCategories.flatMap((category)=>category.components);\n}\nfunction getComponentsByCategory(categoryId) {\n    const category = getCategoryById(categoryId);\n    return category?.components || [];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/design-system.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/Projects/D system/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/Projects/D system/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/Projects/D system/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Layout.tsx */ \"(rsc)/./components/Layout.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFobW91ZG1ldHdhbHklMkZEb2N1bWVudHMlMkZQcm9qZWN0cyUyRkQlMjBzeXN0ZW0lMkZjb21wb25lbnRzJTJGTGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZtYWhtb3VkbWV0d2FseSUyRkRvY3VtZW50cyUyRlByb2plY3RzJTJGRCUyMHN5c3RlbSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGYXBwLWRpciUyRmxpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEpBQXlJO0FBQ3pJO0FBQ0EsZ05BQThLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL21haG1vdWRtZXR3YWx5L0RvY3VtZW50cy9Qcm9qZWN0cy9EIHN5c3RlbS9jb21wb25lbnRzL0xheW91dC50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIl9fZXNNb2R1bGVcIixcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMvbWFobW91ZG1ldHdhbHkvRG9jdW1lbnRzL1Byb2plY3RzL0Qgc3lzdGVtL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2FwcC1kaXIvbGluay5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./components/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-[280px] min-h-screen bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container py-8 px-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFZ0M7QUFNakIsU0FBU0MsT0FBTyxFQUFFQyxRQUFRLEVBQWU7SUFDdEQscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDSixnREFBT0E7Ozs7OzBCQUdSLDhEQUFDRztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ1pGOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyIvVXNlcnMvbWFobW91ZG1ldHdhbHkvRG9jdW1lbnRzL1Byb2plY3RzL0Qgc3lzdGVtL2NvbXBvbmVudHMvTGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBTaWRlYmFyIGZyb20gJy4vU2lkZWJhcic7XG5cbmludGVyZmFjZSBMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExheW91dCh7IGNoaWxkcmVuIH06IExheW91dFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctd2hpdGVcIj5cbiAgICAgIHsvKiBTaWRlYmFyICovfVxuICAgICAgPFNpZGViYXIgLz5cblxuICAgICAgey8qIE1haW4gQ29udGVudCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtWzI4MHB4XSBtaW4taC1zY3JlZW4gYmctd2hpdGVcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgcHktOCBweC04XCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlNpZGViYXIiLCJMYXlvdXQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/Layout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Sidebar.tsx":
/*!********************************!*\
  !*** ./components/Sidebar.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_design_system__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/design-system */ \"(ssr)/./lib/design-system.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Icons for categories (using simple SVG icons)\nconst CategoryIcons = {\n    thumbnail: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M2 3a1 1 0 011-1h10a1 1 0 011 1v10a1 1 0 01-1 1H3a1 1 0 01-1-1V3zm2 1v8h8V4H4z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined),\n    foundations: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M8 1l7 4v6l-7 4-7-4V5l7-4zm0 2L3.5 5.5 8 8l4.5-2.5L8 3zm-5 4v4l5 2.5V9.5L3 7zm10 0L8 9.5V14l5-2.5V7z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined),\n    'shared-components': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M4 2a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h8v8H4V4z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined),\n    'shared-assets': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M8 1a7 7 0 100 14A7 7 0 008 1zM6 6a1 1 0 112 0 1 1 0 01-2 0zm1 3a1 1 0 100 2 1 1 0 000-2z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined),\n    'marketing-website-examples': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M1 2a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H2a1 1 0 01-1-1V2zm2 1v10h10V3H3z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined),\n    'marketing-website-components': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M2 2h12v2H2V2zm0 4h12v2H2V6zm0 4h8v2H2v-2z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined),\n    'application-examples': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M1 1h14v14H1V1zm2 2v10h10V3H3z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined),\n    'application-components': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M3 3h4v4H3V3zm6 0h4v4H9V3zM3 9h4v4H3V9zm6 0h4v4H9V9z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined)\n};\nfunction Sidebar({ categories = _lib_design_system__WEBPACK_IMPORTED_MODULE_4__.designSystemCategories, currentPath }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'foundations'\n    ]));\n    const toggleCategory = (categoryId)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(categoryId)) {\n            newExpanded.delete(categoryId);\n        } else {\n            newExpanded.add(categoryId);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const isActive = (href)=>{\n        return pathname === href;\n    };\n    const isCategoryActive = (categoryId)=>{\n        return pathname.startsWith(`/${categoryId}`);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed left-0 top-0 z-10 h-screen w-[280px] overflow-y-auto border-r bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"Design System\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-gray-500\",\n                        children: \"Chakra UI RSC Library\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: categories.map((category)=>{\n                    const isExpanded = expandedCategories.has(category.id);\n                    const isCatActive = isCategoryActive(category.id);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>toggleCategory(category.id),\n                                className: `w-full flex items-center justify-between p-3 text-left text-sm font-medium rounded transition-colors ${isCatActive ? 'bg-gray-200 text-gray-900' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-4 h-4\",\n                                                children: CategoryIcons[category.id]\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: category.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `w-3 h-3 transition-transform ${isExpanded ? 'rotate-90' : 'rotate-0'}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            viewBox: \"0 0 16 16\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M6 4l4 4-4 4V4z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 15\n                            }, this),\n                            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-6 py-2\",\n                                children: category.components.map((component)=>{\n                                    const componentHref = `/${category.id}/${component.id}`;\n                                    const isComponentActive = isActive(componentHref);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: componentHref,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `block p-2 text-sm rounded transition-colors ${isComponentActive ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}`,\n                                            children: component.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, component.id, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 23\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, category.id, true, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/design-system.ts":
/*!******************************!*\
  !*** ./lib/design-system.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   designSystemCategories: () => (/* binding */ designSystemCategories),\n/* harmony export */   getAllComponents: () => (/* binding */ getAllComponents),\n/* harmony export */   getCategoryById: () => (/* binding */ getCategoryById),\n/* harmony export */   getComponentById: () => (/* binding */ getComponentById),\n/* harmony export */   getComponentsByCategory: () => (/* binding */ getComponentsByCategory)\n/* harmony export */ });\n// Design System Configuration\nconst designSystemCategories = [\n    {\n        id: 'thumbnail',\n        title: 'Thumbnail',\n        description: 'Overview and preview of the design system',\n        components: [\n            {\n                id: 'overview',\n                name: 'Design System Overview',\n                category: 'thumbnail',\n                description: 'Complete overview of the design system components and guidelines',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {}\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'foundations',\n        title: 'Foundations',\n        description: 'Core design tokens, colors, typography, and spacing',\n        components: [\n            {\n                id: 'colors',\n                name: 'Colors',\n                category: 'foundations',\n                description: 'Color palette and semantic color tokens',\n                variants: [\n                    {\n                        name: 'Primary',\n                        props: {\n                            colorScheme: 'blue'\n                        }\n                    },\n                    {\n                        name: 'Secondary',\n                        props: {\n                            colorScheme: 'gray'\n                        }\n                    },\n                    {\n                        name: 'Success',\n                        props: {\n                            colorScheme: 'green'\n                        }\n                    },\n                    {\n                        name: 'Warning',\n                        props: {\n                            colorScheme: 'orange'\n                        }\n                    },\n                    {\n                        name: 'Error',\n                        props: {\n                            colorScheme: 'red'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'typography',\n                name: 'Typography',\n                category: 'foundations',\n                description: 'Font families, sizes, weights, and text styles',\n                variants: [\n                    {\n                        name: 'Headings',\n                        props: {\n                            type: 'headings'\n                        }\n                    },\n                    {\n                        name: 'Body Text',\n                        props: {\n                            type: 'body'\n                        }\n                    },\n                    {\n                        name: 'Captions',\n                        props: {\n                            type: 'captions'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'spacing',\n                name: 'Spacing',\n                category: 'foundations',\n                description: 'Spacing scale and layout tokens',\n                variants: [\n                    {\n                        name: 'Scale',\n                        props: {\n                            type: 'scale'\n                        }\n                    },\n                    {\n                        name: 'Layout',\n                        props: {\n                            type: 'layout'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'shadows',\n                name: 'Shadows',\n                category: 'foundations',\n                description: 'Shadow tokens for depth and elevation',\n                variants: [\n                    {\n                        name: 'Elevation',\n                        props: {\n                            type: 'elevation'\n                        }\n                    },\n                    {\n                        name: 'Focus',\n                        props: {\n                            type: 'focus'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'shared-components',\n        title: 'Shared Components',\n        description: 'Reusable UI components used across applications',\n        components: [\n            {\n                id: 'button',\n                name: 'Button',\n                category: 'shared-components',\n                description: 'Interactive button component with multiple variants',\n                variants: [\n                    {\n                        name: 'Primary',\n                        props: {\n                            variant: 'solid',\n                            colorScheme: 'blue'\n                        }\n                    },\n                    {\n                        name: 'Secondary',\n                        props: {\n                            variant: 'outline',\n                            colorScheme: 'blue'\n                        }\n                    },\n                    {\n                        name: 'Ghost',\n                        props: {\n                            variant: 'ghost',\n                            colorScheme: 'blue'\n                        }\n                    },\n                    {\n                        name: 'Link',\n                        props: {\n                            variant: 'plain',\n                            colorScheme: 'blue'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'input',\n                name: 'Input',\n                category: 'shared-components',\n                description: 'Text input component with validation states',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {\n                            placeholder: 'Enter text...'\n                        }\n                    },\n                    {\n                        name: 'Error',\n                        props: {\n                            invalid: true,\n                            placeholder: 'Invalid input'\n                        }\n                    },\n                    {\n                        name: 'Disabled',\n                        props: {\n                            disabled: true,\n                            placeholder: 'Disabled input'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'card',\n                name: 'Card',\n                category: 'shared-components',\n                description: 'Container component for grouping related content',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {}\n                    },\n                    {\n                        name: 'Elevated',\n                        props: {\n                            variant: 'elevated'\n                        }\n                    },\n                    {\n                        name: 'Outlined',\n                        props: {\n                            variant: 'outline'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'shared-assets',\n        title: 'Shared Assets',\n        description: 'Icons, illustrations, and other visual assets',\n        components: [\n            {\n                id: 'icons',\n                name: 'Icons',\n                category: 'shared-assets',\n                description: 'Icon library and usage guidelines',\n                variants: [\n                    {\n                        name: 'Interface',\n                        props: {\n                            category: 'interface'\n                        }\n                    },\n                    {\n                        name: 'Actions',\n                        props: {\n                            category: 'actions'\n                        }\n                    },\n                    {\n                        name: 'Status',\n                        props: {\n                            category: 'status'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'illustrations',\n                name: 'Illustrations',\n                category: 'shared-assets',\n                description: 'Illustration library for empty states and onboarding',\n                variants: [\n                    {\n                        name: 'Empty States',\n                        props: {\n                            type: 'empty'\n                        }\n                    },\n                    {\n                        name: 'Onboarding',\n                        props: {\n                            type: 'onboarding'\n                        }\n                    },\n                    {\n                        name: 'Error States',\n                        props: {\n                            type: 'error'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'marketing-website-examples',\n        title: 'Marketing Website Examples',\n        description: 'Complete page examples for marketing websites',\n        components: [\n            {\n                id: 'landing-page',\n                name: 'Landing Page',\n                category: 'marketing-website-examples',\n                description: 'Complete landing page layout with hero, features, and CTA',\n                variants: [\n                    {\n                        name: 'SaaS',\n                        props: {\n                            type: 'saas'\n                        }\n                    },\n                    {\n                        name: 'Product',\n                        props: {\n                            type: 'product'\n                        }\n                    },\n                    {\n                        name: 'Agency',\n                        props: {\n                            type: 'agency'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'pricing-page',\n                name: 'Pricing Page',\n                category: 'marketing-website-examples',\n                description: 'Pricing page with plans and feature comparison',\n                variants: [\n                    {\n                        name: 'Simple',\n                        props: {\n                            layout: 'simple'\n                        }\n                    },\n                    {\n                        name: 'Comparison',\n                        props: {\n                            layout: 'comparison'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'marketing-website-components',\n        title: 'Marketing Website Components',\n        description: 'Specialized components for marketing websites',\n        components: [\n            {\n                id: 'hero-section',\n                name: 'Hero Section',\n                category: 'marketing-website-components',\n                description: 'Hero section with headline, description, and CTA',\n                variants: [\n                    {\n                        name: 'Centered',\n                        props: {\n                            alignment: 'center'\n                        }\n                    },\n                    {\n                        name: 'Left Aligned',\n                        props: {\n                            alignment: 'left'\n                        }\n                    },\n                    {\n                        name: 'With Image',\n                        props: {\n                            hasImage: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'feature-grid',\n                name: 'Feature Grid',\n                category: 'marketing-website-components',\n                description: 'Grid layout for showcasing product features',\n                variants: [\n                    {\n                        name: '3 Column',\n                        props: {\n                            columns: 3\n                        }\n                    },\n                    {\n                        name: '4 Column',\n                        props: {\n                            columns: 4\n                        }\n                    },\n                    {\n                        name: 'With Icons',\n                        props: {\n                            hasIcons: true\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'application-examples',\n        title: 'Application Examples',\n        description: 'Complete application page layouts and flows',\n        components: [\n            {\n                id: 'dashboard',\n                name: 'Dashboard',\n                category: 'application-examples',\n                description: 'Complete dashboard layout with sidebar and content area',\n                variants: [\n                    {\n                        name: 'Analytics',\n                        props: {\n                            type: 'analytics'\n                        }\n                    },\n                    {\n                        name: 'CRM',\n                        props: {\n                            type: 'crm'\n                        }\n                    },\n                    {\n                        name: 'E-commerce',\n                        props: {\n                            type: 'ecommerce'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'settings-page',\n                name: 'Settings Page',\n                category: 'application-examples',\n                description: 'Settings page with tabs and form sections',\n                variants: [\n                    {\n                        name: 'Profile',\n                        props: {\n                            section: 'profile'\n                        }\n                    },\n                    {\n                        name: 'Security',\n                        props: {\n                            section: 'security'\n                        }\n                    },\n                    {\n                        name: 'Billing',\n                        props: {\n                            section: 'billing'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'application-components',\n        title: 'Application Components',\n        description: 'Complex components for application interfaces',\n        components: [\n            {\n                id: 'data-table',\n                name: 'Data Table',\n                category: 'application-components',\n                description: 'Advanced data table with sorting, filtering, and pagination',\n                variants: [\n                    {\n                        name: 'Basic',\n                        props: {\n                            features: [\n                                'sorting'\n                            ]\n                        }\n                    },\n                    {\n                        name: 'Advanced',\n                        props: {\n                            features: [\n                                'sorting',\n                                'filtering',\n                                'pagination'\n                            ]\n                        }\n                    },\n                    {\n                        name: 'Selectable',\n                        props: {\n                            selectable: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'sidebar-navigation',\n                name: 'Sidebar Navigation',\n                category: 'application-components',\n                description: 'Collapsible sidebar navigation for applications',\n                variants: [\n                    {\n                        name: 'Expanded',\n                        props: {\n                            collapsed: false\n                        }\n                    },\n                    {\n                        name: 'Collapsed',\n                        props: {\n                            collapsed: true\n                        }\n                    },\n                    {\n                        name: 'With Submenu',\n                        props: {\n                            hasSubmenu: true\n                        }\n                    }\n                ]\n            }\n        ]\n    }\n];\n// Utility functions\nfunction getCategoryById(id) {\n    return designSystemCategories.find((category)=>category.id === id);\n}\nfunction getComponentById(componentId) {\n    for (const category of designSystemCategories){\n        const component = category.components.find((comp)=>comp.id === componentId);\n        if (component) return component;\n    }\n    return undefined;\n}\nfunction getAllComponents() {\n    return designSystemCategories.flatMap((category)=>category.components);\n}\nfunction getComponentsByCategory(categoryId) {\n    const category = getCategoryById(categoryId);\n    return category?.components || [];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/design-system.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Layout.tsx */ \"(ssr)/./components/Layout.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFobW91ZG1ldHdhbHklMkZEb2N1bWVudHMlMkZQcm9qZWN0cyUyRkQlMjBzeXN0ZW0lMkZjb21wb25lbnRzJTJGTGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZtYWhtb3VkbWV0d2FseSUyRkRvY3VtZW50cyUyRlByb2plY3RzJTJGRCUyMHN5c3RlbSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGYXBwLWRpciUyRmxpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEpBQXlJO0FBQ3pJO0FBQ0EsZ05BQThLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL21haG1vdWRtZXR3YWx5L0RvY3VtZW50cy9Qcm9qZWN0cy9EIHN5c3RlbS9jb21wb25lbnRzL0xheW91dC50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIl9fZXNNb2R1bGVcIixcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMvbWFobW91ZG1ldHdhbHkvRG9jdW1lbnRzL1Byb2plY3RzL0Qgc3lzdGVtL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2FwcC1kaXIvbGluay5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();