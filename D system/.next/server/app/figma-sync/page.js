/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/figma-sync/page";
exports.ids = ["app/figma-sync/page"];
exports.modules = {

/***/ "(rsc)/./app/figma-sync/page.tsx":
/*!*********************************!*\
  !*** ./app/figma-sync/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FigmaSyncPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"(rsc)/./components/Layout.tsx\");\n/* harmony import */ var _lib_figma_sync__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/figma-sync */ \"(rsc)/./lib/figma-sync.ts\");\n/* harmony import */ var _lib_figma_integration__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/figma-integration */ \"(rsc)/./lib/figma-integration.ts\");\n/* harmony import */ var _lib_figma_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/figma-components */ \"(rsc)/./lib/figma-components.ts\");\n\n\n\n\n\nasync function FigmaSyncPage() {\n    // In a real implementation, you would use the actual Figma API\n    // For now, we'll use mock data to demonstrate the structure\n    const figmaSync = (0,_lib_figma_sync__WEBPACK_IMPORTED_MODULE_2__.createFigmaSync)(process.env.FIGMA_ACCESS_TOKEN);\n    // Use real synced components from Figma\n    const syncedComponents = _lib_figma_components__WEBPACK_IMPORTED_MODULE_4__.figmaComponents;\n    const syncedTokens = _lib_figma_sync__WEBPACK_IMPORTED_MODULE_2__.mockFigmaTokens; // Still using mock tokens as Figma file has no styles\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-4\",\n                            children: \"Figma Integration\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 mb-6\",\n                            children: \"Real-time synchronization with the Figma design system file. Components and design tokens are automatically synced to maintain consistency between design and code.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"badge badge-green px-3 py-1\",\n                                    children: \"Connected to Figma\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"badge badge-blue px-3 py-1\",\n                                    children: \"Auto-sync Enabled\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-gray-900 mb-4\",\n                            children: \"Connected Figma File\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-gray-700\",\n                                            children: \"File ID:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"design-token\",\n                                            children: _lib_figma_integration__WEBPACK_IMPORTED_MODULE_3__.FIGMA_FILE_ID\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-gray-700\",\n                                            children: \"File URL:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: `https://www.figma.com/design/${_lib_figma_integration__WEBPACK_IMPORTED_MODULE_3__.FIGMA_FILE_ID}/Design-System`,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-blue-600 hover:text-blue-700 underline\",\n                                            children: \"Open in Figma\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-gray-700\",\n                                            children: \"Last Sync:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Just now\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-6\",\n                            children: \"Synced Components\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: syncedComponents.map((component)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full h-[120px] bg-gray-50 rounded border flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Figma Component Preview\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-base font-semibold text-gray-900\",\n                                                                children: component.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                                lineNumber: 84,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"badge badge-purple\",\n                                                                children: \"Figma Synced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-3\",\n                                                        children: component.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    component.figmaUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: component.figmaUrl,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-blue-600 hover:text-blue-700 text-sm underline\",\n                                                        children: \"View in Figma →\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mb-2\",\n                                                        children: [\n                                                            \"Variants (\",\n                                                            component.variants.length,\n                                                            \"):\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-wrap\",\n                                                        children: [\n                                                            component.variants.slice(0, 3).map((variant)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"badge badge-blue\",\n                                                                    children: variant.name\n                                                                }, variant.name, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                                    lineNumber: 115,\n                                                                    columnNumber: 25\n                                                                }, this)),\n                                                            component.variants.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"badge badge-gray\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    component.variants.length - 3,\n                                                                    \" more\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 19\n                                            }, this),\n                                            component.tokens && component.tokens.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mb-2\",\n                                                        children: [\n                                                            \"Design Tokens (\",\n                                                            component.tokens.length,\n                                                            \"):\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col gap-1\",\n                                                        children: [\n                                                            component.tokens.slice(0, 2).map((token)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center text-xs\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: token.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                                            lineNumber: 139,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                            className: \"text-xs bg-gray-100 px-1 rounded\",\n                                                                            children: token.value\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                                            lineNumber: 140,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, token.name, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                                    lineNumber: 138,\n                                                                    columnNumber: 27\n                                                                }, this)),\n                                                            component.tokens.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    component.tokens.length - 2,\n                                                                    \" more tokens\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 17\n                                    }, this)\n                                }, component.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-6\",\n                            children: \"Design Tokens\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Design tokens automatically extracted from Figma styles and components:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: syncedTokens.map((token)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-gray-50 rounded border\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-sm text-gray-900\",\n                                                            children: token.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `badge badge-${token.type === 'color' ? 'blue' : token.type === 'typography' ? 'green' : 'gray'}`,\n                                                            children: token.type\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                            className: \"text-xs bg-white px-2 py-1 rounded border\",\n                                                            children: token.value\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        token.type === 'color' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 rounded border\",\n                                                            style: {\n                                                                backgroundColor: token.value\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                token.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: token.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, token.name, true, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-6\",\n                            children: \"Sync Actions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                            children: \"Manual Sync\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Manually trigger a sync to pull the latest changes from Figma.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn btn-primary\",\n                                            children: \"Sync Now\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                            children: \"Export Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Export design tokens as CSS variables or Chakra UI theme.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"btn btn-secondary\",\n                                                    children: \"Export CSS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"btn btn-secondary\",\n                                                    children: \"Export Theme\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-gray-900 mb-4\",\n                            children: \"Setting Up Figma Integration\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                className: \"list-decimal list-inside space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Generate a Figma access token from your Figma account settings\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"Add the token to your \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                children: \".env.local\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 41\n                                            }, this),\n                                            \" file as \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                children: \"FIGMA_ACCESS_TOKEN\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 73\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Ensure your Figma file follows the design system naming conventions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Components will automatically sync and update the RSC library\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Design tokens are extracted from Figma styles and component properties\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/figma-sync/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cc69884ed9b6\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYWhtb3VkbWV0d2FseS9Eb2N1bWVudHMvUHJvamVjdHMvRCBzeXN0ZW0vYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiY2M2OTg4NGVkOWI2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: 'Design System - Chakra UI RSC Library',\n    description: 'A comprehensive design system built with Next.js 15, React 19, and Chakra UI 3.3',\n    keywords: [\n        'design system',\n        'chakra ui',\n        'react',\n        'next.js',\n        'figma',\n        'components'\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/app/layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/app/layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDdUI7QUFFaEIsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO1FBQUM7UUFBaUI7UUFBYTtRQUFTO1FBQVc7UUFBUztLQUFhO0FBQ3JGLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyx3QkFBd0I7a0JBQ3RDLDRFQUFDQztzQkFDRUo7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYWhtb3VkbWV0d2FseS9Eb2N1bWVudHMvUHJvamVjdHMvRCBzeXN0ZW0vYXBwL2xheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJztcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdEZXNpZ24gU3lzdGVtIC0gQ2hha3JhIFVJIFJTQyBMaWJyYXJ5JyxcbiAgZGVzY3JpcHRpb246ICdBIGNvbXByZWhlbnNpdmUgZGVzaWduIHN5c3RlbSBidWlsdCB3aXRoIE5leHQuanMgMTUsIFJlYWN0IDE5LCBhbmQgQ2hha3JhIFVJIDMuMycsXG4gIGtleXdvcmRzOiBbJ2Rlc2lnbiBzeXN0ZW0nLCAnY2hha3JhIHVpJywgJ3JlYWN0JywgJ25leHQuanMnLCAnZmlnbWEnLCAnY29tcG9uZW50cyddLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cbiAgICAgIDxib2R5PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./lib/figma-components.ts":
/*!*********************************!*\
  !*** ./lib/figma-components.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   figmaComponents: () => (/* binding */ figmaComponents),\n/* harmony export */   figmaEnhancedCategories: () => (/* binding */ figmaEnhancedCategories),\n/* harmony export */   getFigmaComponentById: () => (/* binding */ getFigmaComponentById),\n/* harmony export */   getFigmaComponentsByCategory: () => (/* binding */ getFigmaComponentsByCategory)\n/* harmony export */ });\n// Real Figma components extracted from the design system file\n// Load the synced components from Figma\nconst figmaComponents = [\n    {\n        id: 'logomark',\n        name: 'Logomark',\n        category: 'foundations',\n        description: 'Brand logomark component from Figma design system',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1083:50505',\n        figmaId: '1083:50505',\n        variants: [\n            {\n                name: 'Default',\n                props: {}\n            },\n            {\n                name: 'Small',\n                props: {\n                    size: 'sm'\n                }\n            },\n            {\n                name: 'Large',\n                props: {\n                    size: 'lg'\n                }\n            }\n        ]\n    },\n    {\n        id: 'swatch-base',\n        name: 'Color Swatch',\n        category: 'foundations',\n        description: 'Color swatch component for displaying color tokens',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1029:37647',\n        figmaId: '1029:37647',\n        variants: [\n            {\n                name: 'Solid',\n                props: {\n                    type: 'solid'\n                }\n            },\n            {\n                name: 'Gradient',\n                props: {\n                    type: 'gradient'\n                }\n            }\n        ]\n    },\n    {\n        id: 'type-scale-base',\n        name: 'Typography Scale',\n        category: 'foundations',\n        description: 'Typography scale component showing font sizes and weights',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1019:35537',\n        figmaId: '1019:35537',\n        variants: [\n            {\n                name: 'Display',\n                props: {\n                    category: 'display'\n                }\n            },\n            {\n                name: 'Headings',\n                props: {\n                    category: 'headings'\n                }\n            },\n            {\n                name: 'Body',\n                props: {\n                    category: 'body'\n                }\n            },\n            {\n                name: 'Small',\n                props: {\n                    category: 'small'\n                }\n            }\n        ]\n    },\n    {\n        id: 'button-icon',\n        name: 'Button with Icon',\n        category: 'shared-components',\n        description: 'Button component with icon variants from Figma',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1046:10170',\n        figmaId: '1046:10170',\n        variants: [\n            {\n                name: 'No Icon',\n                props: {\n                    icon: false\n                }\n            },\n            {\n                name: 'Leading Icon',\n                props: {\n                    icon: 'leading'\n                }\n            },\n            {\n                name: 'Trailing Icon',\n                props: {\n                    icon: 'trailing'\n                }\n            }\n        ]\n    },\n    {\n        id: 'size-variants',\n        name: 'Size Variants',\n        category: 'shared-components',\n        description: 'Component size variants (sm, md, lg)',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=2680:401975',\n        figmaId: '2680:401975',\n        variants: [\n            {\n                name: 'Small',\n                props: {\n                    size: 'sm'\n                }\n            },\n            {\n                name: 'Medium',\n                props: {\n                    size: 'md'\n                }\n            },\n            {\n                name: 'Large',\n                props: {\n                    size: 'lg'\n                }\n            }\n        ]\n    },\n    {\n        id: 'design-system-footer',\n        name: 'Design System Footer',\n        category: 'shared-components',\n        description: 'Footer component for design system documentation',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1023:37095',\n        figmaId: '1023:37095',\n        variants: [\n            {\n                name: 'Default',\n                props: {}\n            },\n            {\n                name: 'Compact',\n                props: {\n                    variant: 'compact'\n                }\n            }\n        ]\n    },\n    {\n        id: 'safari-mockup',\n        name: 'Safari Browser Mockup',\n        category: 'shared-assets',\n        description: 'Safari browser mockup for showcasing web applications',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1868:671028',\n        figmaId: '1868:671028',\n        variants: [\n            {\n                name: 'With Address Bar',\n                props: {\n                    addressBar: true\n                }\n            },\n            {\n                name: 'Clean',\n                props: {\n                    addressBar: false\n                }\n            }\n        ]\n    },\n    {\n        id: 'screen-mockup',\n        name: 'Screen Mockup',\n        category: 'shared-assets',\n        description: 'Device screen mockup for presentations',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1316:3497',\n        figmaId: '1316:3497',\n        variants: [\n            {\n                name: 'Desktop',\n                props: {\n                    breakpoint: 'desktop'\n                }\n            },\n            {\n                name: 'Tablet',\n                props: {\n                    breakpoint: 'tablet'\n                }\n            },\n            {\n                name: 'Mobile',\n                props: {\n                    breakpoint: 'mobile'\n                }\n            }\n        ]\n    },\n    {\n        id: 'email-template',\n        name: 'Email Template',\n        category: 'shared-assets',\n        description: 'Email template for user invitations and notifications',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=4057:415518',\n        figmaId: '4057:415518',\n        variants: [\n            {\n                name: 'User Invite',\n                props: {\n                    type: 'user-invite'\n                }\n            },\n            {\n                name: 'Welcome',\n                props: {\n                    type: 'welcome'\n                }\n            },\n            {\n                name: 'Notification',\n                props: {\n                    type: 'notification'\n                }\n            }\n        ]\n    },\n    {\n        id: 'footer-layout',\n        name: 'Footer Layout',\n        category: 'marketing-website-components',\n        description: 'Website footer with multiple column layouts',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1282:12816',\n        figmaId: '1282:12816',\n        variants: [\n            {\n                name: '4 Column',\n                props: {\n                    columns: 4\n                }\n            },\n            {\n                name: '3 Column',\n                props: {\n                    columns: 3\n                }\n            },\n            {\n                name: '2 Column',\n                props: {\n                    columns: 2\n                }\n            }\n        ]\n    },\n    {\n        id: 'maps-integration',\n        name: 'Maps Integration',\n        category: 'application-components',\n        description: 'Google Maps and vector map components',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1438:225984',\n        figmaId: '1438:225984',\n        variants: [\n            {\n                name: 'Google Maps',\n                props: {\n                    type: 'google-maps'\n                }\n            },\n            {\n                name: 'Vector Map',\n                props: {\n                    type: 'vector'\n                }\n            }\n        ]\n    },\n    {\n        id: 'command-bar',\n        name: 'Command Bar',\n        category: 'application-components',\n        description: 'Command palette for quick actions and navigation',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=4898:411379',\n        figmaId: '4898:411379',\n        variants: [\n            {\n                name: 'Default',\n                props: {}\n            },\n            {\n                name: 'With Footer',\n                props: {\n                    footer: true\n                }\n            }\n        ]\n    },\n    {\n        id: 'charts',\n        name: 'Chart Components',\n        category: 'application-components',\n        description: 'Data visualization charts including radar charts',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1084:7152',\n        figmaId: '1084:7152',\n        variants: [\n            {\n                name: 'Radar Chart',\n                props: {\n                    type: 'radar'\n                }\n            },\n            {\n                name: 'Bar Chart',\n                props: {\n                    type: 'bar'\n                }\n            },\n            {\n                name: 'Line Chart',\n                props: {\n                    type: 'line'\n                }\n            }\n        ]\n    },\n    {\n        id: 'notifications',\n        name: 'Notification System',\n        category: 'application-components',\n        description: 'Notification components for desktop applications',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1251:2998',\n        figmaId: '1251:2998',\n        variants: [\n            {\n                name: 'Toast',\n                props: {\n                    type: 'toast'\n                }\n            },\n            {\n                name: 'Banner',\n                props: {\n                    type: 'banner'\n                }\n            },\n            {\n                name: 'Modal',\n                props: {\n                    type: 'modal'\n                }\n            }\n        ]\n    },\n    {\n        id: 'messaging',\n        name: 'Messaging Interface',\n        category: 'application-components',\n        description: 'Chat and messaging components for applications',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1247:167',\n        figmaId: '1247:167',\n        variants: [\n            {\n                name: 'Chat Bubble',\n                props: {\n                    type: 'bubble'\n                }\n            },\n            {\n                name: 'Message List',\n                props: {\n                    type: 'list'\n                }\n            },\n            {\n                name: 'Input Area',\n                props: {\n                    type: 'input'\n                }\n            }\n        ]\n    }\n];\n// Enhanced categories with real Figma components\nconst figmaEnhancedCategories = [\n    {\n        id: 'thumbnail',\n        title: 'Thumbnail',\n        description: 'Overview and preview of the design system',\n        components: [\n            {\n                id: 'overview',\n                name: 'Design System Overview',\n                category: 'thumbnail',\n                description: 'Complete overview of the design system components and guidelines',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=2210-441096',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {}\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'foundations',\n        title: 'Foundations',\n        description: 'Core design tokens, colors, typography, and spacing from Figma',\n        components: figmaComponents.filter((c)=>c.category === 'foundations')\n    },\n    {\n        id: 'shared-components',\n        title: 'Shared Components',\n        description: 'Reusable UI components synced from Figma design system',\n        components: figmaComponents.filter((c)=>c.category === 'shared-components')\n    },\n    {\n        id: 'shared-assets',\n        title: 'Shared Assets',\n        description: 'Icons, mockups, and visual assets from Figma',\n        components: figmaComponents.filter((c)=>c.category === 'shared-assets')\n    },\n    {\n        id: 'marketing-website-examples',\n        title: 'Marketing Website Examples',\n        description: 'Complete page examples for marketing websites',\n        components: [\n            {\n                id: 'landing-pages',\n                name: 'Landing Pages',\n                category: 'marketing-website-examples',\n                description: 'Complete landing page layouts from Figma',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=landing-pages',\n                variants: [\n                    {\n                        name: 'SaaS Landing',\n                        props: {\n                            type: 'saas'\n                        }\n                    },\n                    {\n                        name: 'Product Landing',\n                        props: {\n                            type: 'product'\n                        }\n                    },\n                    {\n                        name: 'Agency Landing',\n                        props: {\n                            type: 'agency'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'pricing-pages',\n                name: 'Pricing Pages',\n                category: 'marketing-website-examples',\n                description: 'Pricing page layouts with different structures',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=pricing-pages',\n                variants: [\n                    {\n                        name: 'Simple Pricing',\n                        props: {\n                            layout: 'simple'\n                        }\n                    },\n                    {\n                        name: 'Feature Comparison',\n                        props: {\n                            layout: 'comparison'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'marketing-website-components',\n        title: 'Marketing Website Components',\n        description: 'Marketing-specific components from Figma',\n        components: figmaComponents.filter((c)=>c.category === 'marketing-website-components')\n    },\n    {\n        id: 'application-examples',\n        title: 'Application Examples',\n        description: 'Complete application layouts from Figma',\n        components: [\n            {\n                id: 'dashboards',\n                name: 'Dashboard Examples',\n                category: 'application-examples',\n                description: 'Complete dashboard layouts from Figma',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=dashboards',\n                variants: [\n                    {\n                        name: 'Analytics Dashboard',\n                        props: {\n                            type: 'analytics'\n                        }\n                    },\n                    {\n                        name: 'CRM Dashboard',\n                        props: {\n                            type: 'crm'\n                        }\n                    },\n                    {\n                        name: 'E-commerce Dashboard',\n                        props: {\n                            type: 'ecommerce'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'application-components',\n        title: 'Application Components',\n        description: 'Complex application components synced from Figma',\n        components: figmaComponents.filter((c)=>c.category === 'application-components')\n    }\n];\n// Function to get Figma component by ID\nfunction getFigmaComponentById(id) {\n    return figmaComponents.find((component)=>component.id === id);\n}\n// Function to get all Figma components by category\nfunction getFigmaComponentsByCategory(category) {\n    return figmaComponents.filter((component)=>component.category === category);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/figma-components.ts\n");

/***/ }),

/***/ "(rsc)/./lib/figma-integration.ts":
/*!**********************************!*\
  !*** ./lib/figma-integration.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FIGMA_FILE_ID: () => (/* binding */ FIGMA_FILE_ID),\n/* harmony export */   FigmaIntegration: () => (/* binding */ FigmaIntegration),\n/* harmony export */   MockFigmaIntegration: () => (/* binding */ MockFigmaIntegration),\n/* harmony export */   createFigmaIntegration: () => (/* binding */ createFigmaIntegration),\n/* harmony export */   extractDesignTokens: () => (/* binding */ extractDesignTokens),\n/* harmony export */   extractDesignTokensFromFigma: () => (/* binding */ extractDesignTokensFromFigma),\n/* harmony export */   figmaColorToCss: () => (/* binding */ figmaColorToCss),\n/* harmony export */   figmaColorToHex: () => (/* binding */ figmaColorToHex),\n/* harmony export */   figmaComponentToRSC: () => (/* binding */ figmaComponentToRSC),\n/* harmony export */   generateChakraTokens: () => (/* binding */ generateChakraTokens),\n/* harmony export */   generateCssCustomProperties: () => (/* binding */ generateCssCustomProperties),\n/* harmony export */   parseFigmaDesignSystem: () => (/* binding */ parseFigmaDesignSystem)\n/* harmony export */ });\n// Figma API Integration utilities\nclass FigmaIntegration {\n    constructor(accessToken){\n        this.baseUrl = 'https://api.figma.com/v1';\n        this.accessToken = accessToken;\n    }\n    // Fetch Figma file data\n    async getFile(fileId) {\n        const response = await fetch(`${this.baseUrl}/files/${fileId}`, {\n            headers: {\n                'X-Figma-Token': this.accessToken\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`Failed to fetch Figma file: ${response.statusText}`);\n        }\n        return response.json();\n    }\n    // Fetch specific node from Figma\n    async getNode(fileId, nodeId) {\n        const response = await fetch(`${this.baseUrl}/files/${fileId}/nodes?ids=${nodeId}`, {\n            headers: {\n                'X-Figma-Token': this.accessToken\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`Failed to fetch Figma node: ${response.statusText}`);\n        }\n        return response.json();\n    }\n    // Export node as image\n    async exportImage(fileId, nodeId, format = 'png', scale = 2) {\n        const response = await fetch(`${this.baseUrl}/images/${fileId}?ids=${nodeId}&format=${format}&scale=${scale}`, {\n            headers: {\n                'X-Figma-Token': this.accessToken\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`Failed to export Figma image: ${response.statusText}`);\n        }\n        return response.json();\n    }\n}\n// Convert Figma color to CSS\nfunction figmaColorToCss(color) {\n    const r = Math.round(color.r * 255);\n    const g = Math.round(color.g * 255);\n    const b = Math.round(color.b * 255);\n    const a = color.a;\n    if (a === 1) {\n        return `rgb(${r}, ${g}, ${b})`;\n    } else {\n        return `rgba(${r}, ${g}, ${b}, ${a})`;\n    }\n}\n// Convert Figma color to hex\nfunction figmaColorToHex(color) {\n    const r = Math.round(color.r * 255);\n    const g = Math.round(color.g * 255);\n    const b = Math.round(color.b * 255);\n    const toHex = (n)=>n.toString(16).padStart(2, '0');\n    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;\n}\n// Extract design tokens from Figma node\nfunction extractDesignTokens(node) {\n    const tokens = [];\n    // Extract color tokens\n    if (node.fills) {\n        node.fills.forEach((fill, index)=>{\n            if (fill.type === 'SOLID' && fill.color) {\n                tokens.push({\n                    name: `${node.name.toLowerCase().replace(/\\s+/g, '-')}-color-${index}`,\n                    value: figmaColorToHex(fill.color),\n                    type: 'color',\n                    description: `Color from ${node.name}`,\n                    figmaId: node.id\n                });\n            }\n        });\n    }\n    // Extract size tokens\n    if (node.absoluteBoundingBox) {\n        tokens.push({\n            name: `${node.name.toLowerCase().replace(/\\s+/g, '-')}-width`,\n            value: node.absoluteBoundingBox.width,\n            type: 'size',\n            description: `Width of ${node.name}`,\n            figmaId: node.id\n        }, {\n            name: `${node.name.toLowerCase().replace(/\\s+/g, '-')}-height`,\n            value: node.absoluteBoundingBox.height,\n            type: 'size',\n            description: `Height of ${node.name}`,\n            figmaId: node.id\n        });\n    }\n    // Extract shadow tokens\n    if (node.effects) {\n        node.effects.forEach((effect, index)=>{\n            if (effect.type === 'DROP_SHADOW' && effect.color) {\n                const shadowValue = `${effect.offset?.x || 0}px ${effect.offset?.y || 0}px ${effect.radius || 0}px ${figmaColorToCss(effect.color)}`;\n                tokens.push({\n                    name: `${node.name.toLowerCase().replace(/\\s+/g, '-')}-shadow-${index}`,\n                    value: shadowValue,\n                    type: 'shadow',\n                    description: `Shadow from ${node.name}`,\n                    figmaId: node.id\n                });\n            }\n        });\n    }\n    return tokens;\n}\n// Generate CSS custom properties from design tokens\nfunction generateCssCustomProperties(tokens) {\n    const cssProperties = tokens.map((token)=>{\n        const propertyName = `--${token.name}`;\n        return `  ${propertyName}: ${token.value};`;\n    });\n    return `:root {\\n${cssProperties.join('\\n')}\\n}`;\n}\n// Generate Chakra UI theme tokens from design tokens\nfunction generateChakraTokens(tokens) {\n    const chakraTokens = {\n        colors: {},\n        sizes: {},\n        shadows: {},\n        spacing: {}\n    };\n    tokens.forEach((token)=>{\n        switch(token.type){\n            case 'color':\n                chakraTokens.colors[token.name] = {\n                    value: token.value\n                };\n                break;\n            case 'size':\n                chakraTokens.sizes[token.name] = {\n                    value: `${token.value}px`\n                };\n                break;\n            case 'shadow':\n                chakraTokens.shadows[token.name] = {\n                    value: token.value\n                };\n                break;\n            case 'spacing':\n                chakraTokens.spacing[token.name] = {\n                    value: `${token.value}px`\n                };\n                break;\n        }\n    });\n    return chakraTokens;\n}\n// Mock Figma integration for development (when no access token is available)\nclass MockFigmaIntegration {\n    async getFile(fileId) {\n        return {\n            document: {\n                id: 'mock-document',\n                name: 'Mock Design System',\n                children: [\n                    {\n                        id: 'mock-page',\n                        name: 'Design System',\n                        children: [\n                            {\n                                id: 'mock-frame',\n                                name: 'Components',\n                                type: 'FRAME',\n                                children: []\n                            }\n                        ]\n                    }\n                ]\n            }\n        };\n    }\n    async getNode(fileId, nodeId) {\n        return {\n            nodes: {\n                [nodeId]: {\n                    document: {\n                        id: nodeId,\n                        name: 'Mock Component',\n                        type: 'COMPONENT',\n                        fills: [\n                            {\n                                type: 'SOLID',\n                                color: {\n                                    r: 0.2,\n                                    g: 0.4,\n                                    b: 0.8,\n                                    a: 1\n                                }\n                            }\n                        ],\n                        absoluteBoundingBox: {\n                            x: 0,\n                            y: 0,\n                            width: 200,\n                            height: 100\n                        }\n                    }\n                }\n            }\n        };\n    }\n    async exportImage(fileId, nodeId) {\n        return {\n            images: {\n                [nodeId]: 'https://via.placeholder.com/400x200/3182ce/ffffff?text=Mock+Component'\n            }\n        };\n    }\n}\n// Create Figma integration instance\nfunction createFigmaIntegration(accessToken) {\n    if (accessToken) {\n        return new FigmaIntegration(accessToken);\n    } else {\n        return new MockFigmaIntegration();\n    }\n}\n// Figma file specific utilities\nconst FIGMA_FILE_ID = 'CYlewnUysOEtOe6BVlSlZ3';\n// Parse Figma design system structure\nfunction parseFigmaDesignSystem(figmaData) {\n    const designSystemStructure = {\n        foundations: {\n            colors: [],\n            typography: [],\n            spacing: [],\n            shadows: []\n        },\n        components: {\n            buttons: [],\n            inputs: [],\n            cards: [],\n            navigation: []\n        },\n        patterns: {\n            layouts: [],\n            forms: [],\n            dataDisplay: []\n        }\n    };\n    // This would parse the actual Figma structure\n    // For now, return mock structure based on the URL structure\n    return designSystemStructure;\n}\n// Extract design tokens from Figma frames\nfunction extractDesignTokensFromFigma(figmaNode) {\n    const tokens = [];\n    // Extract color tokens from fills\n    if (figmaNode.fills) {\n        figmaNode.fills.forEach((fill, index)=>{\n            if (fill.type === 'SOLID' && fill.color) {\n                tokens.push({\n                    name: `${figmaNode.name.toLowerCase().replace(/\\s+/g, '-')}-${index}`,\n                    value: figmaColorToHex(fill.color),\n                    type: 'color',\n                    description: `Color from ${figmaNode.name}`,\n                    figmaId: figmaNode.id\n                });\n            }\n        });\n    }\n    // Extract typography tokens\n    if (figmaNode.style) {\n        const style = figmaNode.style;\n        if (style.fontSize) {\n            tokens.push({\n                name: `${figmaNode.name.toLowerCase().replace(/\\s+/g, '-')}-font-size`,\n                value: `${style.fontSize}px`,\n                type: 'typography',\n                description: `Font size from ${figmaNode.name}`,\n                figmaId: figmaNode.id\n            });\n        }\n    }\n    return tokens;\n}\n// Convert Figma component to RSC structure\nfunction figmaComponentToRSC(figmaComponent) {\n    return {\n        id: figmaComponent.name.toLowerCase().replace(/\\s+/g, '-'),\n        name: figmaComponent.name,\n        description: figmaComponent.description || `${figmaComponent.name} component from Figma`,\n        figmaId: figmaComponent.id,\n        figmaUrl: `https://www.figma.com/design/${FIGMA_FILE_ID}?node-id=${figmaComponent.id}`,\n        variants: figmaComponent.children?.map((child)=>({\n                name: child.name,\n                props: extractPropsFromFigmaVariant(child),\n                figmaId: child.id\n            })) || [],\n        tokens: extractDesignTokensFromFigma(figmaComponent)\n    };\n}\n// Extract props from Figma variant\nfunction extractPropsFromFigmaVariant(figmaVariant) {\n    const props = {};\n    // Extract common props from Figma properties\n    if (figmaVariant.name.includes('Primary')) {\n        props.variant = 'primary';\n    } else if (figmaVariant.name.includes('Secondary')) {\n        props.variant = 'secondary';\n    } else if (figmaVariant.name.includes('Outline')) {\n        props.variant = 'outline';\n    }\n    if (figmaVariant.name.includes('Large')) {\n        props.size = 'lg';\n    } else if (figmaVariant.name.includes('Small')) {\n        props.size = 'sm';\n    } else {\n        props.size = 'md';\n    }\n    // Extract color scheme\n    if (figmaVariant.name.includes('Blue')) {\n        props.colorScheme = 'blue';\n    } else if (figmaVariant.name.includes('Green')) {\n        props.colorScheme = 'green';\n    } else if (figmaVariant.name.includes('Red')) {\n        props.colorScheme = 'red';\n    }\n    return props;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/figma-integration.ts\n");

/***/ }),

/***/ "(rsc)/./lib/figma-sync.ts":
/*!***************************!*\
  !*** ./lib/figma-sync.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FigmaDesignSystemSync: () => (/* binding */ FigmaDesignSystemSync),\n/* harmony export */   createFigmaSync: () => (/* binding */ createFigmaSync),\n/* harmony export */   mockFigmaComponents: () => (/* binding */ mockFigmaComponents),\n/* harmony export */   mockFigmaTokens: () => (/* binding */ mockFigmaTokens)\n/* harmony export */ });\n/* harmony import */ var _figma_integration__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./figma-integration */ \"(rsc)/./lib/figma-integration.ts\");\n\n// Figma sync utilities for the specific design system file\nclass FigmaDesignSystemSync {\n    constructor(accessToken){\n        this.figmaIntegration = (0,_figma_integration__WEBPACK_IMPORTED_MODULE_0__.createFigmaIntegration)(accessToken);\n        this.fileId = _figma_integration__WEBPACK_IMPORTED_MODULE_0__.FIGMA_FILE_ID;\n    }\n    // Sync all components from Figma\n    async syncAllComponents() {\n        try {\n            const fileData = await this.figmaIntegration.getFile(this.fileId);\n            const components = [];\n            // Parse the Figma file structure\n            if (fileData.document && fileData.document.children) {\n                for (const page of fileData.document.children){\n                    if (page.name.toLowerCase().includes('components') || page.name.toLowerCase().includes('design system')) {\n                        const pageComponents = await this.parsePageComponents(page);\n                        components.push(...pageComponents);\n                    }\n                }\n            }\n            return components;\n        } catch (error) {\n            console.error('Error syncing components from Figma:', error);\n            return [];\n        }\n    }\n    // Parse components from a Figma page\n    async parsePageComponents(page) {\n        const components = [];\n        if (page.children) {\n            for (const frame of page.children){\n                if (frame.type === 'FRAME' || frame.type === 'COMPONENT_SET') {\n                    const component = (0,_figma_integration__WEBPACK_IMPORTED_MODULE_0__.figmaComponentToRSC)(frame);\n                    // Categorize component based on frame name or position\n                    component.category = this.categorizeComponent(frame.name);\n                    components.push(component);\n                }\n            }\n        }\n        return components;\n    }\n    // Categorize component based on its name or properties\n    categorizeComponent(componentName) {\n        const name = componentName.toLowerCase();\n        if (name.includes('color') || name.includes('typography') || name.includes('spacing') || name.includes('shadow')) {\n            return 'foundations';\n        } else if (name.includes('button') || name.includes('input') || name.includes('card') || name.includes('badge')) {\n            return 'shared-components';\n        } else if (name.includes('icon') || name.includes('illustration')) {\n            return 'shared-assets';\n        } else if (name.includes('marketing') || name.includes('landing')) {\n            return 'marketing-website-components';\n        } else if (name.includes('dashboard') || name.includes('table') || name.includes('navigation')) {\n            return 'application-components';\n        }\n        return 'shared-components'; // default\n    }\n    // Sync design tokens from Figma\n    async syncDesignTokens() {\n        try {\n            const fileData = await this.figmaIntegration.getFile(this.fileId);\n            const tokens = [];\n            // Look for design tokens in styles\n            if (fileData.styles) {\n                for (const [styleId, style] of Object.entries(fileData.styles)){\n                    const token = this.convertStyleToToken(style, styleId);\n                    if (token) {\n                        tokens.push(token);\n                    }\n                }\n            }\n            return tokens;\n        } catch (error) {\n            console.error('Error syncing design tokens from Figma:', error);\n            return [];\n        }\n    }\n    // Convert Figma style to design token\n    convertStyleToToken(style, styleId) {\n        if (!style.name) return null;\n        const tokenName = style.name.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n        // Determine token type based on style type\n        let tokenType = 'color';\n        let tokenValue = '';\n        switch(style.styleType){\n            case 'FILL':\n                tokenType = 'color';\n                // Extract color value from style\n                tokenValue = '#000000'; // placeholder\n                break;\n            case 'TEXT':\n                tokenType = 'typography';\n                tokenValue = `${style.fontSize || 16}px`;\n                break;\n            case 'EFFECT':\n                tokenType = 'shadow';\n                tokenValue = '0 2px 4px rgba(0,0,0,0.1)'; // placeholder\n                break;\n            default:\n                return null;\n        }\n        return {\n            name: tokenName,\n            value: tokenValue,\n            type: tokenType,\n            description: style.description || `${style.name} from Figma`,\n            figmaId: styleId\n        };\n    }\n    // Export component as image from Figma\n    async exportComponentImage(componentId, format = 'png') {\n        try {\n            const result = await this.figmaIntegration.exportImage(this.fileId, componentId, format);\n            return result.images?.[componentId] || null;\n        } catch (error) {\n            console.error('Error exporting component image:', error);\n            return null;\n        }\n    }\n    // Get component details from Figma\n    async getComponentDetails(componentId) {\n        try {\n            const result = await this.figmaIntegration.getNode(this.fileId, componentId);\n            return result.nodes?.[componentId]?.document || null;\n        } catch (error) {\n            console.error('Error getting component details:', error);\n            return null;\n        }\n    }\n    // Generate CSS from design tokens\n    generateCSSFromTokens(tokens) {\n        const cssVariables = tokens.map((token)=>{\n            return `  --${token.name}: ${token.value};`;\n        }).join('\\n');\n        return `:root {\\n${cssVariables}\\n}`;\n    }\n    // Generate Chakra UI theme from design tokens\n    generateChakraTheme(tokens) {\n        const theme = {\n            colors: {},\n            fontSizes: {},\n            space: {},\n            shadows: {}\n        };\n        tokens.forEach((token)=>{\n            switch(token.type){\n                case 'color':\n                    theme.colors[token.name] = {\n                        value: token.value\n                    };\n                    break;\n                case 'typography':\n                    theme.fontSizes[token.name] = {\n                        value: token.value\n                    };\n                    break;\n                case 'spacing':\n                    theme.space[token.name] = {\n                        value: token.value\n                    };\n                    break;\n                case 'shadow':\n                    theme.shadows[token.name] = {\n                        value: token.value\n                    };\n                    break;\n            }\n        });\n        return theme;\n    }\n}\n// Utility function to create a sync instance\nfunction createFigmaSync(accessToken) {\n    return new FigmaDesignSystemSync(accessToken);\n}\n// Mock data for development when Figma token is not available\nconst mockFigmaComponents = [\n    {\n        id: 'figma-button',\n        name: 'Figma Button',\n        category: 'shared-components',\n        description: 'Button component synced from Figma design system',\n        figmaUrl: `https://www.figma.com/design/${_figma_integration__WEBPACK_IMPORTED_MODULE_0__.FIGMA_FILE_ID}?node-id=button-component`,\n        variants: [\n            {\n                name: 'Primary',\n                props: {\n                    variant: 'primary',\n                    size: 'md'\n                }\n            },\n            {\n                name: 'Secondary',\n                props: {\n                    variant: 'secondary',\n                    size: 'md'\n                }\n            },\n            {\n                name: 'Outline',\n                props: {\n                    variant: 'outline',\n                    size: 'md'\n                }\n            }\n        ],\n        tokens: [\n            {\n                name: 'button-primary-bg',\n                value: '#3b82f6',\n                type: 'color',\n                description: 'Primary button background'\n            },\n            {\n                name: 'button-padding-x',\n                value: '16px',\n                type: 'spacing',\n                description: 'Button horizontal padding'\n            },\n            {\n                name: 'button-border-radius',\n                value: '6px',\n                type: 'border',\n                description: 'Button border radius'\n            }\n        ]\n    }\n];\nconst mockFigmaTokens = [\n    {\n        name: 'primary-50',\n        value: '#eff6ff',\n        type: 'color',\n        description: 'Primary color lightest shade'\n    },\n    {\n        name: 'primary-500',\n        value: '#3b82f6',\n        type: 'color',\n        description: 'Primary color base'\n    },\n    {\n        name: 'primary-900',\n        value: '#1e3a8a',\n        type: 'color',\n        description: 'Primary color darkest shade'\n    },\n    {\n        name: 'font-size-sm',\n        value: '14px',\n        type: 'typography',\n        description: 'Small font size'\n    },\n    {\n        name: 'font-size-base',\n        value: '16px',\n        type: 'typography',\n        description: 'Base font size'\n    },\n    {\n        name: 'font-size-lg',\n        value: '18px',\n        type: 'typography',\n        description: 'Large font size'\n    },\n    {\n        name: 'space-2',\n        value: '8px',\n        type: 'spacing',\n        description: 'Small spacing'\n    },\n    {\n        name: 'space-4',\n        value: '16px',\n        type: 'spacing',\n        description: 'Base spacing'\n    },\n    {\n        name: 'space-6',\n        value: '24px',\n        type: 'spacing',\n        description: 'Large spacing'\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/figma-sync.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffigma-sync%2Fpage&page=%2Ffigma-sync%2Fpage&appPaths=%2Ffigma-sync%2Fpage&pagePath=private-next-app-dir%2Ffigma-sync%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffigma-sync%2Fpage&page=%2Ffigma-sync%2Fpage&appPaths=%2Ffigma-sync%2Fpage&pagePath=private-next-app-dir%2Ffigma-sync%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/figma-sync/page.tsx */ \"(rsc)/./app/figma-sync/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'figma-sync',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/Projects/D system/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/Projects/D system/app/figma-sync/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/figma-sync/page\",\n        pathname: \"/figma-sync\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffigma-sync%2Fpage&page=%2Ffigma-sync%2Fpage&appPaths=%2Ffigma-sync%2Fpage&pagePath=private-next-app-dir%2Ffigma-sync%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Layout.tsx */ \"(rsc)/./components/Layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFobW91ZG1ldHdhbHklMkZEb2N1bWVudHMlMkZQcm9qZWN0cyUyRkQlMjBzeXN0ZW0lMkZjb21wb25lbnRzJTJGTGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUF5SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9tYWhtb3VkbWV0d2FseS9Eb2N1bWVudHMvUHJvamVjdHMvRCBzeXN0ZW0vY29tcG9uZW50cy9MYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./components/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-[280px] min-h-screen bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container py-8 px-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFZ0M7QUFNakIsU0FBU0MsT0FBTyxFQUFFQyxRQUFRLEVBQWU7SUFDdEQscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDSixnREFBT0E7Ozs7OzBCQUdSLDhEQUFDRztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ1pGOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyIvVXNlcnMvbWFobW91ZG1ldHdhbHkvRG9jdW1lbnRzL1Byb2plY3RzL0Qgc3lzdGVtL2NvbXBvbmVudHMvTGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBTaWRlYmFyIGZyb20gJy4vU2lkZWJhcic7XG5cbmludGVyZmFjZSBMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExheW91dCh7IGNoaWxkcmVuIH06IExheW91dFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctd2hpdGVcIj5cbiAgICAgIHsvKiBTaWRlYmFyICovfVxuICAgICAgPFNpZGViYXIgLz5cblxuICAgICAgey8qIE1haW4gQ29udGVudCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtWzI4MHB4XSBtaW4taC1zY3JlZW4gYmctd2hpdGVcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgcHktOCBweC04XCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlNpZGViYXIiLCJMYXlvdXQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/Layout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Sidebar.tsx":
/*!********************************!*\
  !*** ./components/Sidebar.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_design_system__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/design-system */ \"(ssr)/./lib/design-system.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Icons for categories (using simple SVG icons)\nconst CategoryIcons = {\n    thumbnail: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M2 3a1 1 0 011-1h10a1 1 0 011 1v10a1 1 0 01-1 1H3a1 1 0 01-1-1V3zm2 1v8h8V4H4z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined),\n    foundations: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M8 1l7 4v6l-7 4-7-4V5l7-4zm0 2L3.5 5.5 8 8l4.5-2.5L8 3zm-5 4v4l5 2.5V9.5L3 7zm10 0L8 9.5V14l5-2.5V7z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined),\n    'shared-components': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M4 2a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h8v8H4V4z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined),\n    'shared-assets': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M8 1a7 7 0 100 14A7 7 0 008 1zM6 6a1 1 0 112 0 1 1 0 01-2 0zm1 3a1 1 0 100 2 1 1 0 000-2z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined),\n    'marketing-website-examples': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M1 2a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H2a1 1 0 01-1-1V2zm2 1v10h10V3H3z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined),\n    'marketing-website-components': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M2 2h12v2H2V2zm0 4h12v2H2V6zm0 4h8v2H2v-2z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined),\n    'application-examples': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M1 1h14v14H1V1zm2 2v10h10V3H3z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined),\n    'application-components': /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M3 3h4v4H3V3zm6 0h4v4H9V3zM3 9h4v4H3V9zm6 0h4v4H9V9z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined)\n};\nfunction Sidebar({ categories = _lib_design_system__WEBPACK_IMPORTED_MODULE_4__.designSystemCategories, currentPath }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'foundations'\n    ]));\n    const toggleCategory = (categoryId)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(categoryId)) {\n            newExpanded.delete(categoryId);\n        } else {\n            newExpanded.add(categoryId);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const isActive = (href)=>{\n        return pathname === href;\n    };\n    const isCategoryActive = (categoryId)=>{\n        return pathname.startsWith(`/${categoryId}`);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed left-0 top-0 z-10 h-screen w-[280px] overflow-y-auto border-r bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"Design System\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-gray-500\",\n                        children: \"Chakra UI RSC Library\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/figma-sync\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        viewBox: \"0 0 16 16\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M8 1a7 7 0 100 14A7 7 0 008 1zM6 6a1 1 0 112 0 1 1 0 01-2 0zm1 3a1 1 0 100 2 1 1 0 000-2z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Figma Sync\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: categories.map((category)=>{\n                    const isExpanded = expandedCategories.has(category.id);\n                    const isCatActive = isCategoryActive(category.id);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>toggleCategory(category.id),\n                                className: `w-full flex items-center justify-between p-3 text-left text-sm font-medium rounded transition-colors ${isCatActive ? 'bg-gray-200 text-gray-900' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-4 h-4\",\n                                                children: CategoryIcons[category.id]\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: category.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `w-3 h-3 transition-transform ${isExpanded ? 'rotate-90' : 'rotate-0'}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            viewBox: \"0 0 16 16\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M6 4l4 4-4 4V4z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 15\n                            }, this),\n                            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-6 py-2\",\n                                children: category.components.map((component)=>{\n                                    const componentHref = `/${category.id}/${component.id}`;\n                                    const isComponentActive = isActive(componentHref);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: componentHref,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `block p-2 text-sm rounded transition-colors ${isComponentActive ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}`,\n                                            children: component.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, component.id, false, {\n                                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 23\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, category.id, true, {\n                        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Projects/D system/components/Sidebar.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/design-system.ts":
/*!******************************!*\
  !*** ./lib/design-system.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   designSystemCategories: () => (/* binding */ designSystemCategories),\n/* harmony export */   getAllComponents: () => (/* binding */ getAllComponents),\n/* harmony export */   getCategoryById: () => (/* binding */ getCategoryById),\n/* harmony export */   getComponentById: () => (/* binding */ getComponentById),\n/* harmony export */   getComponentsByCategory: () => (/* binding */ getComponentsByCategory),\n/* harmony export */   originalDesignSystemCategories: () => (/* binding */ originalDesignSystemCategories)\n/* harmony export */ });\n/* harmony import */ var _figma_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./figma-components */ \"(ssr)/./lib/figma-components.ts\");\n\n// Design System Configuration based on Figma file\n// Use Figma-enhanced categories that include real components from the design system\nconst designSystemCategories = _figma_components__WEBPACK_IMPORTED_MODULE_0__.figmaEnhancedCategories;\n// Original design system categories (kept for reference)\nconst originalDesignSystemCategories = [\n    {\n        id: 'thumbnail',\n        title: 'Thumbnail',\n        description: 'Overview and preview of the design system',\n        components: [\n            {\n                id: 'overview',\n                name: 'Design System Overview',\n                category: 'thumbnail',\n                description: 'Complete overview of the design system components and guidelines',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=2210-441096',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {}\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'foundations',\n        title: 'Foundations',\n        description: 'Core design tokens, colors, typography, and spacing',\n        components: [\n            {\n                id: 'colors',\n                name: 'Color System',\n                category: 'foundations',\n                description: 'Primary, secondary, and semantic color palettes with accessibility guidelines',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=colors',\n                variants: [\n                    {\n                        name: 'Primary Palette',\n                        props: {\n                            palette: 'primary'\n                        }\n                    },\n                    {\n                        name: 'Secondary Palette',\n                        props: {\n                            palette: 'secondary'\n                        }\n                    },\n                    {\n                        name: 'Semantic Colors',\n                        props: {\n                            palette: 'semantic'\n                        }\n                    },\n                    {\n                        name: 'Neutral Colors',\n                        props: {\n                            palette: 'neutral'\n                        }\n                    },\n                    {\n                        name: 'Brand Colors',\n                        props: {\n                            palette: 'brand'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'primary-50',\n                        value: '#eff6ff',\n                        type: 'color',\n                        description: 'Primary color lightest shade'\n                    },\n                    {\n                        name: 'primary-500',\n                        value: '#3b82f6',\n                        type: 'color',\n                        description: 'Primary color base'\n                    },\n                    {\n                        name: 'primary-900',\n                        value: '#1e3a8a',\n                        type: 'color',\n                        description: 'Primary color darkest shade'\n                    }\n                ]\n            },\n            {\n                id: 'typography',\n                name: 'Typography Scale',\n                category: 'foundations',\n                description: 'Font families, sizes, weights, and line heights for consistent text hierarchy',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=typography',\n                variants: [\n                    {\n                        name: 'Display Text',\n                        props: {\n                            category: 'display'\n                        }\n                    },\n                    {\n                        name: 'Headings',\n                        props: {\n                            category: 'headings'\n                        }\n                    },\n                    {\n                        name: 'Body Text',\n                        props: {\n                            category: 'body'\n                        }\n                    },\n                    {\n                        name: 'Labels & Captions',\n                        props: {\n                            category: 'labels'\n                        }\n                    },\n                    {\n                        name: 'Code & Monospace',\n                        props: {\n                            category: 'code'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'font-size-xs',\n                        value: '12px',\n                        type: 'typography',\n                        description: 'Extra small text size'\n                    },\n                    {\n                        name: 'font-size-base',\n                        value: '16px',\n                        type: 'typography',\n                        description: 'Base text size'\n                    },\n                    {\n                        name: 'font-size-2xl',\n                        value: '24px',\n                        type: 'typography',\n                        description: 'Large heading size'\n                    }\n                ]\n            },\n            {\n                id: 'spacing',\n                name: 'Spacing System',\n                category: 'foundations',\n                description: '8px grid system for consistent spacing and layout',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=spacing',\n                variants: [\n                    {\n                        name: 'Base Scale',\n                        props: {\n                            scale: 'base'\n                        }\n                    },\n                    {\n                        name: 'Component Spacing',\n                        props: {\n                            scale: 'component'\n                        }\n                    },\n                    {\n                        name: 'Layout Spacing',\n                        props: {\n                            scale: 'layout'\n                        }\n                    },\n                    {\n                        name: 'Responsive Spacing',\n                        props: {\n                            scale: 'responsive'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'space-1',\n                        value: '4px',\n                        type: 'spacing',\n                        description: 'Smallest spacing unit'\n                    },\n                    {\n                        name: 'space-4',\n                        value: '16px',\n                        type: 'spacing',\n                        description: 'Base spacing unit'\n                    },\n                    {\n                        name: 'space-8',\n                        value: '32px',\n                        type: 'spacing',\n                        description: 'Large spacing unit'\n                    }\n                ]\n            },\n            {\n                id: 'elevation',\n                name: 'Elevation & Shadows',\n                category: 'foundations',\n                description: 'Shadow system for creating depth and visual hierarchy',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=elevation',\n                variants: [\n                    {\n                        name: 'Card Shadows',\n                        props: {\n                            type: 'card'\n                        }\n                    },\n                    {\n                        name: 'Modal Shadows',\n                        props: {\n                            type: 'modal'\n                        }\n                    },\n                    {\n                        name: 'Focus Rings',\n                        props: {\n                            type: 'focus'\n                        }\n                    },\n                    {\n                        name: 'Dropdown Shadows',\n                        props: {\n                            type: 'dropdown'\n                        }\n                    }\n                ],\n                tokens: [\n                    {\n                        name: 'shadow-sm',\n                        value: '0 1px 2px 0 rgb(0 0 0 / 0.05)',\n                        type: 'shadow',\n                        description: 'Small shadow'\n                    },\n                    {\n                        name: 'shadow-md',\n                        value: '0 4px 6px -1px rgb(0 0 0 / 0.1)',\n                        type: 'shadow',\n                        description: 'Medium shadow'\n                    },\n                    {\n                        name: 'shadow-lg',\n                        value: '0 10px 15px -3px rgb(0 0 0 / 0.1)',\n                        type: 'shadow',\n                        description: 'Large shadow'\n                    }\n                ]\n            },\n            {\n                id: 'grid',\n                name: 'Grid System',\n                category: 'foundations',\n                description: 'Responsive grid system and layout guidelines',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=grid',\n                variants: [\n                    {\n                        name: '12 Column Grid',\n                        props: {\n                            columns: 12\n                        }\n                    },\n                    {\n                        name: 'Responsive Breakpoints',\n                        props: {\n                            type: 'breakpoints'\n                        }\n                    },\n                    {\n                        name: 'Container Sizes',\n                        props: {\n                            type: 'containers'\n                        }\n                    },\n                    {\n                        name: 'Gutters & Margins',\n                        props: {\n                            type: 'gutters'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'shared-components',\n        title: 'Shared Components',\n        description: 'Reusable UI components used across applications',\n        components: [\n            {\n                id: 'button',\n                name: 'Button',\n                category: 'shared-components',\n                description: 'Primary interactive element with multiple variants and states',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=button',\n                variants: [\n                    {\n                        name: 'Primary',\n                        props: {\n                            variant: 'primary',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Secondary',\n                        props: {\n                            variant: 'secondary',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Outline',\n                        props: {\n                            variant: 'outline',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Ghost',\n                        props: {\n                            variant: 'ghost',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Destructive',\n                        props: {\n                            variant: 'destructive',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Small',\n                        props: {\n                            variant: 'primary',\n                            size: 'sm'\n                        }\n                    },\n                    {\n                        name: 'Large',\n                        props: {\n                            variant: 'primary',\n                            size: 'lg'\n                        }\n                    },\n                    {\n                        name: 'Icon Only',\n                        props: {\n                            variant: 'primary',\n                            iconOnly: true\n                        }\n                    },\n                    {\n                        name: 'Loading',\n                        props: {\n                            variant: 'primary',\n                            loading: true\n                        }\n                    },\n                    {\n                        name: 'Disabled',\n                        props: {\n                            variant: 'primary',\n                            disabled: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'input',\n                name: 'Input Field',\n                category: 'shared-components',\n                description: 'Text input with labels, validation, and helper text',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=input',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {\n                            placeholder: 'Enter text...'\n                        }\n                    },\n                    {\n                        name: 'With Label',\n                        props: {\n                            label: 'Email Address',\n                            placeholder: 'Enter your email'\n                        }\n                    },\n                    {\n                        name: 'Required',\n                        props: {\n                            label: 'Password',\n                            required: true,\n                            type: 'password'\n                        }\n                    },\n                    {\n                        name: 'Error State',\n                        props: {\n                            label: 'Username',\n                            error: 'Username is already taken'\n                        }\n                    },\n                    {\n                        name: 'Success State',\n                        props: {\n                            label: 'Email',\n                            success: 'Email is available'\n                        }\n                    },\n                    {\n                        name: 'Disabled',\n                        props: {\n                            label: 'Disabled Field',\n                            disabled: true\n                        }\n                    },\n                    {\n                        name: 'With Icon',\n                        props: {\n                            label: 'Search',\n                            icon: 'search',\n                            placeholder: 'Search...'\n                        }\n                    },\n                    {\n                        name: 'Textarea',\n                        props: {\n                            label: 'Message',\n                            type: 'textarea',\n                            rows: 4\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'card',\n                name: 'Card',\n                category: 'shared-components',\n                description: 'Flexible container for grouping related content',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=card',\n                variants: [\n                    {\n                        name: 'Basic Card',\n                        props: {\n                            variant: 'default'\n                        }\n                    },\n                    {\n                        name: 'Elevated Card',\n                        props: {\n                            variant: 'elevated'\n                        }\n                    },\n                    {\n                        name: 'Outlined Card',\n                        props: {\n                            variant: 'outlined'\n                        }\n                    },\n                    {\n                        name: 'Interactive Card',\n                        props: {\n                            variant: 'interactive',\n                            clickable: true\n                        }\n                    },\n                    {\n                        name: 'Product Card',\n                        props: {\n                            variant: 'product',\n                            image: true,\n                            badge: true\n                        }\n                    },\n                    {\n                        name: 'Profile Card',\n                        props: {\n                            variant: 'profile',\n                            avatar: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'badge',\n                name: 'Badge',\n                category: 'shared-components',\n                description: 'Small status indicators and labels',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=badge',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {\n                            variant: 'default'\n                        }\n                    },\n                    {\n                        name: 'Primary',\n                        props: {\n                            variant: 'primary'\n                        }\n                    },\n                    {\n                        name: 'Success',\n                        props: {\n                            variant: 'success'\n                        }\n                    },\n                    {\n                        name: 'Warning',\n                        props: {\n                            variant: 'warning'\n                        }\n                    },\n                    {\n                        name: 'Error',\n                        props: {\n                            variant: 'error'\n                        }\n                    },\n                    {\n                        name: 'Outline',\n                        props: {\n                            variant: 'outline'\n                        }\n                    },\n                    {\n                        name: 'Dot Indicator',\n                        props: {\n                            variant: 'dot'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'avatar',\n                name: 'Avatar',\n                category: 'shared-components',\n                description: 'User profile pictures and initials',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=avatar',\n                variants: [\n                    {\n                        name: 'Image Avatar',\n                        props: {\n                            type: 'image',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Initials',\n                        props: {\n                            type: 'initials',\n                            initials: 'JD',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Icon Avatar',\n                        props: {\n                            type: 'icon',\n                            size: 'md'\n                        }\n                    },\n                    {\n                        name: 'Small',\n                        props: {\n                            type: 'image',\n                            size: 'sm'\n                        }\n                    },\n                    {\n                        name: 'Large',\n                        props: {\n                            type: 'image',\n                            size: 'lg'\n                        }\n                    },\n                    {\n                        name: 'With Status',\n                        props: {\n                            type: 'image',\n                            status: 'online',\n                            size: 'md'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'dropdown',\n                name: 'Dropdown Menu',\n                category: 'shared-components',\n                description: 'Contextual menus and select components',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=dropdown',\n                variants: [\n                    {\n                        name: 'Basic Dropdown',\n                        props: {\n                            variant: 'basic'\n                        }\n                    },\n                    {\n                        name: 'With Icons',\n                        props: {\n                            variant: 'icons'\n                        }\n                    },\n                    {\n                        name: 'With Dividers',\n                        props: {\n                            variant: 'dividers'\n                        }\n                    },\n                    {\n                        name: 'Multi-select',\n                        props: {\n                            variant: 'multiselect'\n                        }\n                    },\n                    {\n                        name: 'Searchable',\n                        props: {\n                            variant: 'searchable'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'shared-assets',\n        title: 'Shared Assets',\n        description: 'Icons, illustrations, and other visual assets',\n        components: [\n            {\n                id: 'icons',\n                name: 'Icons',\n                category: 'shared-assets',\n                description: 'Icon library and usage guidelines',\n                variants: [\n                    {\n                        name: 'Interface',\n                        props: {\n                            category: 'interface'\n                        }\n                    },\n                    {\n                        name: 'Actions',\n                        props: {\n                            category: 'actions'\n                        }\n                    },\n                    {\n                        name: 'Status',\n                        props: {\n                            category: 'status'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'illustrations',\n                name: 'Illustrations',\n                category: 'shared-assets',\n                description: 'Illustration library for empty states and onboarding',\n                variants: [\n                    {\n                        name: 'Empty States',\n                        props: {\n                            type: 'empty'\n                        }\n                    },\n                    {\n                        name: 'Onboarding',\n                        props: {\n                            type: 'onboarding'\n                        }\n                    },\n                    {\n                        name: 'Error States',\n                        props: {\n                            type: 'error'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'marketing-website-examples',\n        title: 'Marketing Website Examples',\n        description: 'Complete page examples for marketing websites',\n        components: [\n            {\n                id: 'landing-page',\n                name: 'Landing Page',\n                category: 'marketing-website-examples',\n                description: 'Complete landing page layout with hero, features, and CTA',\n                variants: [\n                    {\n                        name: 'SaaS',\n                        props: {\n                            type: 'saas'\n                        }\n                    },\n                    {\n                        name: 'Product',\n                        props: {\n                            type: 'product'\n                        }\n                    },\n                    {\n                        name: 'Agency',\n                        props: {\n                            type: 'agency'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'pricing-page',\n                name: 'Pricing Page',\n                category: 'marketing-website-examples',\n                description: 'Pricing page with plans and feature comparison',\n                variants: [\n                    {\n                        name: 'Simple',\n                        props: {\n                            layout: 'simple'\n                        }\n                    },\n                    {\n                        name: 'Comparison',\n                        props: {\n                            layout: 'comparison'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'marketing-website-components',\n        title: 'Marketing Website Components',\n        description: 'Specialized components for marketing websites',\n        components: [\n            {\n                id: 'hero-section',\n                name: 'Hero Section',\n                category: 'marketing-website-components',\n                description: 'Hero section with headline, description, and CTA',\n                variants: [\n                    {\n                        name: 'Centered',\n                        props: {\n                            alignment: 'center'\n                        }\n                    },\n                    {\n                        name: 'Left Aligned',\n                        props: {\n                            alignment: 'left'\n                        }\n                    },\n                    {\n                        name: 'With Image',\n                        props: {\n                            hasImage: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'feature-grid',\n                name: 'Feature Grid',\n                category: 'marketing-website-components',\n                description: 'Grid layout for showcasing product features',\n                variants: [\n                    {\n                        name: '3 Column',\n                        props: {\n                            columns: 3\n                        }\n                    },\n                    {\n                        name: '4 Column',\n                        props: {\n                            columns: 4\n                        }\n                    },\n                    {\n                        name: 'With Icons',\n                        props: {\n                            hasIcons: true\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'application-examples',\n        title: 'Application Examples',\n        description: 'Complete application page layouts and flows',\n        components: [\n            {\n                id: 'dashboard',\n                name: 'Dashboard',\n                category: 'application-examples',\n                description: 'Complete dashboard layout with sidebar and content area',\n                variants: [\n                    {\n                        name: 'Analytics',\n                        props: {\n                            type: 'analytics'\n                        }\n                    },\n                    {\n                        name: 'CRM',\n                        props: {\n                            type: 'crm'\n                        }\n                    },\n                    {\n                        name: 'E-commerce',\n                        props: {\n                            type: 'ecommerce'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'settings-page',\n                name: 'Settings Page',\n                category: 'application-examples',\n                description: 'Settings page with tabs and form sections',\n                variants: [\n                    {\n                        name: 'Profile',\n                        props: {\n                            section: 'profile'\n                        }\n                    },\n                    {\n                        name: 'Security',\n                        props: {\n                            section: 'security'\n                        }\n                    },\n                    {\n                        name: 'Billing',\n                        props: {\n                            section: 'billing'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'application-components',\n        title: 'Application Components',\n        description: 'Complex components for application interfaces',\n        components: [\n            {\n                id: 'data-table',\n                name: 'Data Table',\n                category: 'application-components',\n                description: 'Advanced data table with sorting, filtering, and pagination',\n                variants: [\n                    {\n                        name: 'Basic',\n                        props: {\n                            features: [\n                                'sorting'\n                            ]\n                        }\n                    },\n                    {\n                        name: 'Advanced',\n                        props: {\n                            features: [\n                                'sorting',\n                                'filtering',\n                                'pagination'\n                            ]\n                        }\n                    },\n                    {\n                        name: 'Selectable',\n                        props: {\n                            selectable: true\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'sidebar-navigation',\n                name: 'Sidebar Navigation',\n                category: 'application-components',\n                description: 'Collapsible sidebar navigation for applications',\n                variants: [\n                    {\n                        name: 'Expanded',\n                        props: {\n                            collapsed: false\n                        }\n                    },\n                    {\n                        name: 'Collapsed',\n                        props: {\n                            collapsed: true\n                        }\n                    },\n                    {\n                        name: 'With Submenu',\n                        props: {\n                            hasSubmenu: true\n                        }\n                    }\n                ]\n            }\n        ]\n    }\n];\n// Utility functions\nfunction getCategoryById(id) {\n    return designSystemCategories.find((category)=>category.id === id);\n}\nfunction getComponentById(componentId) {\n    for (const category of designSystemCategories){\n        const component = category.components.find((comp)=>comp.id === componentId);\n        if (component) return component;\n    }\n    return undefined;\n}\nfunction getAllComponents() {\n    return designSystemCategories.flatMap((category)=>category.components);\n}\nfunction getComponentsByCategory(categoryId) {\n    const category = getCategoryById(categoryId);\n    return category?.components || [];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/design-system.ts\n");

/***/ }),

/***/ "(ssr)/./lib/figma-components.ts":
/*!*********************************!*\
  !*** ./lib/figma-components.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   figmaComponents: () => (/* binding */ figmaComponents),\n/* harmony export */   figmaEnhancedCategories: () => (/* binding */ figmaEnhancedCategories),\n/* harmony export */   getFigmaComponentById: () => (/* binding */ getFigmaComponentById),\n/* harmony export */   getFigmaComponentsByCategory: () => (/* binding */ getFigmaComponentsByCategory)\n/* harmony export */ });\n// Real Figma components extracted from the design system file\n// Load the synced components from Figma\nconst figmaComponents = [\n    {\n        id: 'logomark',\n        name: 'Logomark',\n        category: 'foundations',\n        description: 'Brand logomark component from Figma design system',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1083:50505',\n        figmaId: '1083:50505',\n        variants: [\n            {\n                name: 'Default',\n                props: {}\n            },\n            {\n                name: 'Small',\n                props: {\n                    size: 'sm'\n                }\n            },\n            {\n                name: 'Large',\n                props: {\n                    size: 'lg'\n                }\n            }\n        ]\n    },\n    {\n        id: 'swatch-base',\n        name: 'Color Swatch',\n        category: 'foundations',\n        description: 'Color swatch component for displaying color tokens',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1029:37647',\n        figmaId: '1029:37647',\n        variants: [\n            {\n                name: 'Solid',\n                props: {\n                    type: 'solid'\n                }\n            },\n            {\n                name: 'Gradient',\n                props: {\n                    type: 'gradient'\n                }\n            }\n        ]\n    },\n    {\n        id: 'type-scale-base',\n        name: 'Typography Scale',\n        category: 'foundations',\n        description: 'Typography scale component showing font sizes and weights',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1019:35537',\n        figmaId: '1019:35537',\n        variants: [\n            {\n                name: 'Display',\n                props: {\n                    category: 'display'\n                }\n            },\n            {\n                name: 'Headings',\n                props: {\n                    category: 'headings'\n                }\n            },\n            {\n                name: 'Body',\n                props: {\n                    category: 'body'\n                }\n            },\n            {\n                name: 'Small',\n                props: {\n                    category: 'small'\n                }\n            }\n        ]\n    },\n    {\n        id: 'button-icon',\n        name: 'Button with Icon',\n        category: 'shared-components',\n        description: 'Button component with icon variants from Figma',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1046:10170',\n        figmaId: '1046:10170',\n        variants: [\n            {\n                name: 'No Icon',\n                props: {\n                    icon: false\n                }\n            },\n            {\n                name: 'Leading Icon',\n                props: {\n                    icon: 'leading'\n                }\n            },\n            {\n                name: 'Trailing Icon',\n                props: {\n                    icon: 'trailing'\n                }\n            }\n        ]\n    },\n    {\n        id: 'size-variants',\n        name: 'Size Variants',\n        category: 'shared-components',\n        description: 'Component size variants (sm, md, lg)',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=2680:401975',\n        figmaId: '2680:401975',\n        variants: [\n            {\n                name: 'Small',\n                props: {\n                    size: 'sm'\n                }\n            },\n            {\n                name: 'Medium',\n                props: {\n                    size: 'md'\n                }\n            },\n            {\n                name: 'Large',\n                props: {\n                    size: 'lg'\n                }\n            }\n        ]\n    },\n    {\n        id: 'design-system-footer',\n        name: 'Design System Footer',\n        category: 'shared-components',\n        description: 'Footer component for design system documentation',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1023:37095',\n        figmaId: '1023:37095',\n        variants: [\n            {\n                name: 'Default',\n                props: {}\n            },\n            {\n                name: 'Compact',\n                props: {\n                    variant: 'compact'\n                }\n            }\n        ]\n    },\n    {\n        id: 'safari-mockup',\n        name: 'Safari Browser Mockup',\n        category: 'shared-assets',\n        description: 'Safari browser mockup for showcasing web applications',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1868:671028',\n        figmaId: '1868:671028',\n        variants: [\n            {\n                name: 'With Address Bar',\n                props: {\n                    addressBar: true\n                }\n            },\n            {\n                name: 'Clean',\n                props: {\n                    addressBar: false\n                }\n            }\n        ]\n    },\n    {\n        id: 'screen-mockup',\n        name: 'Screen Mockup',\n        category: 'shared-assets',\n        description: 'Device screen mockup for presentations',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1316:3497',\n        figmaId: '1316:3497',\n        variants: [\n            {\n                name: 'Desktop',\n                props: {\n                    breakpoint: 'desktop'\n                }\n            },\n            {\n                name: 'Tablet',\n                props: {\n                    breakpoint: 'tablet'\n                }\n            },\n            {\n                name: 'Mobile',\n                props: {\n                    breakpoint: 'mobile'\n                }\n            }\n        ]\n    },\n    {\n        id: 'email-template',\n        name: 'Email Template',\n        category: 'shared-assets',\n        description: 'Email template for user invitations and notifications',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=4057:415518',\n        figmaId: '4057:415518',\n        variants: [\n            {\n                name: 'User Invite',\n                props: {\n                    type: 'user-invite'\n                }\n            },\n            {\n                name: 'Welcome',\n                props: {\n                    type: 'welcome'\n                }\n            },\n            {\n                name: 'Notification',\n                props: {\n                    type: 'notification'\n                }\n            }\n        ]\n    },\n    {\n        id: 'footer-layout',\n        name: 'Footer Layout',\n        category: 'marketing-website-components',\n        description: 'Website footer with multiple column layouts',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1282:12816',\n        figmaId: '1282:12816',\n        variants: [\n            {\n                name: '4 Column',\n                props: {\n                    columns: 4\n                }\n            },\n            {\n                name: '3 Column',\n                props: {\n                    columns: 3\n                }\n            },\n            {\n                name: '2 Column',\n                props: {\n                    columns: 2\n                }\n            }\n        ]\n    },\n    {\n        id: 'maps-integration',\n        name: 'Maps Integration',\n        category: 'application-components',\n        description: 'Google Maps and vector map components',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1438:225984',\n        figmaId: '1438:225984',\n        variants: [\n            {\n                name: 'Google Maps',\n                props: {\n                    type: 'google-maps'\n                }\n            },\n            {\n                name: 'Vector Map',\n                props: {\n                    type: 'vector'\n                }\n            }\n        ]\n    },\n    {\n        id: 'command-bar',\n        name: 'Command Bar',\n        category: 'application-components',\n        description: 'Command palette for quick actions and navigation',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=4898:411379',\n        figmaId: '4898:411379',\n        variants: [\n            {\n                name: 'Default',\n                props: {}\n            },\n            {\n                name: 'With Footer',\n                props: {\n                    footer: true\n                }\n            }\n        ]\n    },\n    {\n        id: 'charts',\n        name: 'Chart Components',\n        category: 'application-components',\n        description: 'Data visualization charts including radar charts',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1084:7152',\n        figmaId: '1084:7152',\n        variants: [\n            {\n                name: 'Radar Chart',\n                props: {\n                    type: 'radar'\n                }\n            },\n            {\n                name: 'Bar Chart',\n                props: {\n                    type: 'bar'\n                }\n            },\n            {\n                name: 'Line Chart',\n                props: {\n                    type: 'line'\n                }\n            }\n        ]\n    },\n    {\n        id: 'notifications',\n        name: 'Notification System',\n        category: 'application-components',\n        description: 'Notification components for desktop applications',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1251:2998',\n        figmaId: '1251:2998',\n        variants: [\n            {\n                name: 'Toast',\n                props: {\n                    type: 'toast'\n                }\n            },\n            {\n                name: 'Banner',\n                props: {\n                    type: 'banner'\n                }\n            },\n            {\n                name: 'Modal',\n                props: {\n                    type: 'modal'\n                }\n            }\n        ]\n    },\n    {\n        id: 'messaging',\n        name: 'Messaging Interface',\n        category: 'application-components',\n        description: 'Chat and messaging components for applications',\n        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1247:167',\n        figmaId: '1247:167',\n        variants: [\n            {\n                name: 'Chat Bubble',\n                props: {\n                    type: 'bubble'\n                }\n            },\n            {\n                name: 'Message List',\n                props: {\n                    type: 'list'\n                }\n            },\n            {\n                name: 'Input Area',\n                props: {\n                    type: 'input'\n                }\n            }\n        ]\n    }\n];\n// Enhanced categories with real Figma components\nconst figmaEnhancedCategories = [\n    {\n        id: 'thumbnail',\n        title: 'Thumbnail',\n        description: 'Overview and preview of the design system',\n        components: [\n            {\n                id: 'overview',\n                name: 'Design System Overview',\n                category: 'thumbnail',\n                description: 'Complete overview of the design system components and guidelines',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=2210-441096',\n                variants: [\n                    {\n                        name: 'Default',\n                        props: {}\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'foundations',\n        title: 'Foundations',\n        description: 'Core design tokens, colors, typography, and spacing from Figma',\n        components: figmaComponents.filter((c)=>c.category === 'foundations')\n    },\n    {\n        id: 'shared-components',\n        title: 'Shared Components',\n        description: 'Reusable UI components synced from Figma design system',\n        components: figmaComponents.filter((c)=>c.category === 'shared-components')\n    },\n    {\n        id: 'shared-assets',\n        title: 'Shared Assets',\n        description: 'Icons, mockups, and visual assets from Figma',\n        components: figmaComponents.filter((c)=>c.category === 'shared-assets')\n    },\n    {\n        id: 'marketing-website-examples',\n        title: 'Marketing Website Examples',\n        description: 'Complete page examples for marketing websites',\n        components: [\n            {\n                id: 'landing-pages',\n                name: 'Landing Pages',\n                category: 'marketing-website-examples',\n                description: 'Complete landing page layouts from Figma',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=landing-pages',\n                variants: [\n                    {\n                        name: 'SaaS Landing',\n                        props: {\n                            type: 'saas'\n                        }\n                    },\n                    {\n                        name: 'Product Landing',\n                        props: {\n                            type: 'product'\n                        }\n                    },\n                    {\n                        name: 'Agency Landing',\n                        props: {\n                            type: 'agency'\n                        }\n                    }\n                ]\n            },\n            {\n                id: 'pricing-pages',\n                name: 'Pricing Pages',\n                category: 'marketing-website-examples',\n                description: 'Pricing page layouts with different structures',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=pricing-pages',\n                variants: [\n                    {\n                        name: 'Simple Pricing',\n                        props: {\n                            layout: 'simple'\n                        }\n                    },\n                    {\n                        name: 'Feature Comparison',\n                        props: {\n                            layout: 'comparison'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'marketing-website-components',\n        title: 'Marketing Website Components',\n        description: 'Marketing-specific components from Figma',\n        components: figmaComponents.filter((c)=>c.category === 'marketing-website-components')\n    },\n    {\n        id: 'application-examples',\n        title: 'Application Examples',\n        description: 'Complete application layouts from Figma',\n        components: [\n            {\n                id: 'dashboards',\n                name: 'Dashboard Examples',\n                category: 'application-examples',\n                description: 'Complete dashboard layouts from Figma',\n                figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=dashboards',\n                variants: [\n                    {\n                        name: 'Analytics Dashboard',\n                        props: {\n                            type: 'analytics'\n                        }\n                    },\n                    {\n                        name: 'CRM Dashboard',\n                        props: {\n                            type: 'crm'\n                        }\n                    },\n                    {\n                        name: 'E-commerce Dashboard',\n                        props: {\n                            type: 'ecommerce'\n                        }\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'application-components',\n        title: 'Application Components',\n        description: 'Complex application components synced from Figma',\n        components: figmaComponents.filter((c)=>c.category === 'application-components')\n    }\n];\n// Function to get Figma component by ID\nfunction getFigmaComponentById(id) {\n    return figmaComponents.find((component)=>component.id === id);\n}\n// Function to get all Figma components by category\nfunction getFigmaComponentsByCategory(category) {\n    return figmaComponents.filter((component)=>component.category === category);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/figma-components.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Layout.tsx */ \"(ssr)/./components/Layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFobW91ZG1ldHdhbHklMkZEb2N1bWVudHMlMkZQcm9qZWN0cyUyRkQlMjBzeXN0ZW0lMkZjb21wb25lbnRzJTJGTGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUF5SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9tYWhtb3VkbWV0d2FseS9Eb2N1bWVudHMvUHJvamVjdHMvRCBzeXN0ZW0vY29tcG9uZW50cy9MYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fcomponents%2FLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffigma-sync%2Fpage&page=%2Ffigma-sync%2Fpage&appPaths=%2Ffigma-sync%2Fpage&pagePath=private-next-app-dir%2Ffigma-sync%2Fpage.tsx&appDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmahmoudmetwaly%2FDocuments%2FProjects%2FD%20system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();