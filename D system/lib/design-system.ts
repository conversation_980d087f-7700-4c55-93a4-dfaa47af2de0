import { CategoryConfig, DesignSystemCategory, DesignSystemComponent } from '@/types/design-system';

// Design System Configuration based on Figma file
export const designSystemCategories: CategoryConfig[] = [
  {
    id: 'thumbnail',
    title: 'Thumbnail',
    description: 'Overview and preview of the design system',
    components: [
      {
        id: 'overview',
        name: 'Design System Overview',
        category: 'thumbnail',
        description: 'Complete overview of the design system components and guidelines',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=2210-441096',
        variants: [
          { name: 'Default', props: {} }
        ]
      }
    ]
  },
  {
    id: 'foundations',
    title: 'Foundations',
    description: 'Core design tokens, colors, typography, and spacing',
    components: [
      {
        id: 'colors',
        name: 'Color System',
        category: 'foundations',
        description: 'Primary, secondary, and semantic color palettes with accessibility guidelines',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=colors',
        variants: [
          { name: 'Primary Palette', props: { palette: 'primary' } },
          { name: 'Secondary Palette', props: { palette: 'secondary' } },
          { name: 'Semantic Colors', props: { palette: 'semantic' } },
          { name: 'Neutral Colors', props: { palette: 'neutral' } },
          { name: 'Brand Colors', props: { palette: 'brand' } }
        ],
        tokens: [
          { name: 'primary-50', value: '#eff6ff', type: 'color', description: 'Primary color lightest shade' },
          { name: 'primary-500', value: '#3b82f6', type: 'color', description: 'Primary color base' },
          { name: 'primary-900', value: '#1e3a8a', type: 'color', description: 'Primary color darkest shade' }
        ]
      },
      {
        id: 'typography',
        name: 'Typography Scale',
        category: 'foundations',
        description: 'Font families, sizes, weights, and line heights for consistent text hierarchy',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=typography',
        variants: [
          { name: 'Display Text', props: { category: 'display' } },
          { name: 'Headings', props: { category: 'headings' } },
          { name: 'Body Text', props: { category: 'body' } },
          { name: 'Labels & Captions', props: { category: 'labels' } },
          { name: 'Code & Monospace', props: { category: 'code' } }
        ],
        tokens: [
          { name: 'font-size-xs', value: '12px', type: 'typography', description: 'Extra small text size' },
          { name: 'font-size-base', value: '16px', type: 'typography', description: 'Base text size' },
          { name: 'font-size-2xl', value: '24px', type: 'typography', description: 'Large heading size' }
        ]
      },
      {
        id: 'spacing',
        name: 'Spacing System',
        category: 'foundations',
        description: '8px grid system for consistent spacing and layout',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=spacing',
        variants: [
          { name: 'Base Scale', props: { scale: 'base' } },
          { name: 'Component Spacing', props: { scale: 'component' } },
          { name: 'Layout Spacing', props: { scale: 'layout' } },
          { name: 'Responsive Spacing', props: { scale: 'responsive' } }
        ],
        tokens: [
          { name: 'space-1', value: '4px', type: 'spacing', description: 'Smallest spacing unit' },
          { name: 'space-4', value: '16px', type: 'spacing', description: 'Base spacing unit' },
          { name: 'space-8', value: '32px', type: 'spacing', description: 'Large spacing unit' }
        ]
      },
      {
        id: 'elevation',
        name: 'Elevation & Shadows',
        category: 'foundations',
        description: 'Shadow system for creating depth and visual hierarchy',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=elevation',
        variants: [
          { name: 'Card Shadows', props: { type: 'card' } },
          { name: 'Modal Shadows', props: { type: 'modal' } },
          { name: 'Focus Rings', props: { type: 'focus' } },
          { name: 'Dropdown Shadows', props: { type: 'dropdown' } }
        ],
        tokens: [
          { name: 'shadow-sm', value: '0 1px 2px 0 rgb(0 0 0 / 0.05)', type: 'shadow', description: 'Small shadow' },
          { name: 'shadow-md', value: '0 4px 6px -1px rgb(0 0 0 / 0.1)', type: 'shadow', description: 'Medium shadow' },
          { name: 'shadow-lg', value: '0 10px 15px -3px rgb(0 0 0 / 0.1)', type: 'shadow', description: 'Large shadow' }
        ]
      },
      {
        id: 'grid',
        name: 'Grid System',
        category: 'foundations',
        description: 'Responsive grid system and layout guidelines',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=grid',
        variants: [
          { name: '12 Column Grid', props: { columns: 12 } },
          { name: 'Responsive Breakpoints', props: { type: 'breakpoints' } },
          { name: 'Container Sizes', props: { type: 'containers' } },
          { name: 'Gutters & Margins', props: { type: 'gutters' } }
        ]
      }
    ]
  },
  {
    id: 'shared-components',
    title: 'Shared Components',
    description: 'Reusable UI components used across applications',
    components: [
      {
        id: 'button',
        name: 'Button',
        category: 'shared-components',
        description: 'Primary interactive element with multiple variants and states',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=button',
        variants: [
          { name: 'Primary', props: { variant: 'primary', size: 'md' } },
          { name: 'Secondary', props: { variant: 'secondary', size: 'md' } },
          { name: 'Outline', props: { variant: 'outline', size: 'md' } },
          { name: 'Ghost', props: { variant: 'ghost', size: 'md' } },
          { name: 'Destructive', props: { variant: 'destructive', size: 'md' } },
          { name: 'Small', props: { variant: 'primary', size: 'sm' } },
          { name: 'Large', props: { variant: 'primary', size: 'lg' } },
          { name: 'Icon Only', props: { variant: 'primary', iconOnly: true } },
          { name: 'Loading', props: { variant: 'primary', loading: true } },
          { name: 'Disabled', props: { variant: 'primary', disabled: true } }
        ]
      },
      {
        id: 'input',
        name: 'Input Field',
        category: 'shared-components',
        description: 'Text input with labels, validation, and helper text',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=input',
        variants: [
          { name: 'Default', props: { placeholder: 'Enter text...' } },
          { name: 'With Label', props: { label: 'Email Address', placeholder: 'Enter your email' } },
          { name: 'Required', props: { label: 'Password', required: true, type: 'password' } },
          { name: 'Error State', props: { label: 'Username', error: 'Username is already taken' } },
          { name: 'Success State', props: { label: 'Email', success: 'Email is available' } },
          { name: 'Disabled', props: { label: 'Disabled Field', disabled: true } },
          { name: 'With Icon', props: { label: 'Search', icon: 'search', placeholder: 'Search...' } },
          { name: 'Textarea', props: { label: 'Message', type: 'textarea', rows: 4 } }
        ]
      },
      {
        id: 'card',
        name: 'Card',
        category: 'shared-components',
        description: 'Flexible container for grouping related content',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=card',
        variants: [
          { name: 'Basic Card', props: { variant: 'default' } },
          { name: 'Elevated Card', props: { variant: 'elevated' } },
          { name: 'Outlined Card', props: { variant: 'outlined' } },
          { name: 'Interactive Card', props: { variant: 'interactive', clickable: true } },
          { name: 'Product Card', props: { variant: 'product', image: true, badge: true } },
          { name: 'Profile Card', props: { variant: 'profile', avatar: true } }
        ]
      },
      {
        id: 'badge',
        name: 'Badge',
        category: 'shared-components',
        description: 'Small status indicators and labels',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=badge',
        variants: [
          { name: 'Default', props: { variant: 'default' } },
          { name: 'Primary', props: { variant: 'primary' } },
          { name: 'Success', props: { variant: 'success' } },
          { name: 'Warning', props: { variant: 'warning' } },
          { name: 'Error', props: { variant: 'error' } },
          { name: 'Outline', props: { variant: 'outline' } },
          { name: 'Dot Indicator', props: { variant: 'dot' } }
        ]
      },
      {
        id: 'avatar',
        name: 'Avatar',
        category: 'shared-components',
        description: 'User profile pictures and initials',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=avatar',
        variants: [
          { name: 'Image Avatar', props: { type: 'image', size: 'md' } },
          { name: 'Initials', props: { type: 'initials', initials: 'JD', size: 'md' } },
          { name: 'Icon Avatar', props: { type: 'icon', size: 'md' } },
          { name: 'Small', props: { type: 'image', size: 'sm' } },
          { name: 'Large', props: { type: 'image', size: 'lg' } },
          { name: 'With Status', props: { type: 'image', status: 'online', size: 'md' } }
        ]
      },
      {
        id: 'dropdown',
        name: 'Dropdown Menu',
        category: 'shared-components',
        description: 'Contextual menus and select components',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=dropdown',
        variants: [
          { name: 'Basic Dropdown', props: { variant: 'basic' } },
          { name: 'With Icons', props: { variant: 'icons' } },
          { name: 'With Dividers', props: { variant: 'dividers' } },
          { name: 'Multi-select', props: { variant: 'multiselect' } },
          { name: 'Searchable', props: { variant: 'searchable' } }
        ]
      }
    ]
  },
  {
    id: 'shared-assets',
    title: 'Shared Assets',
    description: 'Icons, illustrations, and other visual assets',
    components: [
      {
        id: 'icons',
        name: 'Icons',
        category: 'shared-assets',
        description: 'Icon library and usage guidelines',
        variants: [
          { name: 'Interface', props: { category: 'interface' } },
          { name: 'Actions', props: { category: 'actions' } },
          { name: 'Status', props: { category: 'status' } }
        ]
      },
      {
        id: 'illustrations',
        name: 'Illustrations',
        category: 'shared-assets',
        description: 'Illustration library for empty states and onboarding',
        variants: [
          { name: 'Empty States', props: { type: 'empty' } },
          { name: 'Onboarding', props: { type: 'onboarding' } },
          { name: 'Error States', props: { type: 'error' } }
        ]
      }
    ]
  },
  {
    id: 'marketing-website-examples',
    title: 'Marketing Website Examples',
    description: 'Complete page examples for marketing websites',
    components: [
      {
        id: 'landing-page',
        name: 'Landing Page',
        category: 'marketing-website-examples',
        description: 'Complete landing page layout with hero, features, and CTA',
        variants: [
          { name: 'SaaS', props: { type: 'saas' } },
          { name: 'Product', props: { type: 'product' } },
          { name: 'Agency', props: { type: 'agency' } }
        ]
      },
      {
        id: 'pricing-page',
        name: 'Pricing Page',
        category: 'marketing-website-examples',
        description: 'Pricing page with plans and feature comparison',
        variants: [
          { name: 'Simple', props: { layout: 'simple' } },
          { name: 'Comparison', props: { layout: 'comparison' } }
        ]
      }
    ]
  },
  {
    id: 'marketing-website-components',
    title: 'Marketing Website Components',
    description: 'Specialized components for marketing websites',
    components: [
      {
        id: 'hero-section',
        name: 'Hero Section',
        category: 'marketing-website-components',
        description: 'Hero section with headline, description, and CTA',
        variants: [
          { name: 'Centered', props: { alignment: 'center' } },
          { name: 'Left Aligned', props: { alignment: 'left' } },
          { name: 'With Image', props: { hasImage: true } }
        ]
      },
      {
        id: 'feature-grid',
        name: 'Feature Grid',
        category: 'marketing-website-components',
        description: 'Grid layout for showcasing product features',
        variants: [
          { name: '3 Column', props: { columns: 3 } },
          { name: '4 Column', props: { columns: 4 } },
          { name: 'With Icons', props: { hasIcons: true } }
        ]
      }
    ]
  },
  {
    id: 'application-examples',
    title: 'Application Examples',
    description: 'Complete application page layouts and flows',
    components: [
      {
        id: 'dashboard',
        name: 'Dashboard',
        category: 'application-examples',
        description: 'Complete dashboard layout with sidebar and content area',
        variants: [
          { name: 'Analytics', props: { type: 'analytics' } },
          { name: 'CRM', props: { type: 'crm' } },
          { name: 'E-commerce', props: { type: 'ecommerce' } }
        ]
      },
      {
        id: 'settings-page',
        name: 'Settings Page',
        category: 'application-examples',
        description: 'Settings page with tabs and form sections',
        variants: [
          { name: 'Profile', props: { section: 'profile' } },
          { name: 'Security', props: { section: 'security' } },
          { name: 'Billing', props: { section: 'billing' } }
        ]
      }
    ]
  },
  {
    id: 'application-components',
    title: 'Application Components',
    description: 'Complex components for application interfaces',
    components: [
      {
        id: 'data-table',
        name: 'Data Table',
        category: 'application-components',
        description: 'Advanced data table with sorting, filtering, and pagination',
        variants: [
          { name: 'Basic', props: { features: ['sorting'] } },
          { name: 'Advanced', props: { features: ['sorting', 'filtering', 'pagination'] } },
          { name: 'Selectable', props: { selectable: true } }
        ]
      },
      {
        id: 'sidebar-navigation',
        name: 'Sidebar Navigation',
        category: 'application-components',
        description: 'Collapsible sidebar navigation for applications',
        variants: [
          { name: 'Expanded', props: { collapsed: false } },
          { name: 'Collapsed', props: { collapsed: true } },
          { name: 'With Submenu', props: { hasSubmenu: true } }
        ]
      }
    ]
  }
];

// Utility functions
export function getCategoryById(id: DesignSystemCategory): CategoryConfig | undefined {
  return designSystemCategories.find(category => category.id === id);
}

export function getComponentById(componentId: string): DesignSystemComponent | undefined {
  for (const category of designSystemCategories) {
    const component = category.components.find(comp => comp.id === componentId);
    if (component) return component;
  }
  return undefined;
}

export function getAllComponents(): DesignSystemComponent[] {
  return designSystemCategories.flatMap(category => category.components);
}

export function getComponentsByCategory(categoryId: DesignSystemCategory): DesignSystemComponent[] {
  const category = getCategoryById(categoryId);
  return category?.components || [];
}
