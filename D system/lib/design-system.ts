import { CategoryConfig, DesignSystemCategory, DesignSystemComponent } from '@/types/design-system';

// Design System Configuration
export const designSystemCategories: CategoryConfig[] = [
  {
    id: 'thumbnail',
    title: 'Thumbnail',
    description: 'Overview and preview of the design system',
    components: [
      {
        id: 'overview',
        name: 'Design System Overview',
        category: 'thumbnail',
        description: 'Complete overview of the design system components and guidelines',
        variants: [
          { name: 'Default', props: {} }
        ]
      }
    ]
  },
  {
    id: 'foundations',
    title: 'Foundations',
    description: 'Core design tokens, colors, typography, and spacing',
    components: [
      {
        id: 'colors',
        name: 'Colors',
        category: 'foundations',
        description: 'Color palette and semantic color tokens',
        variants: [
          { name: 'Primary', props: { colorScheme: 'blue' } },
          { name: 'Secondary', props: { colorScheme: 'gray' } },
          { name: 'Success', props: { colorScheme: 'green' } },
          { name: 'Warning', props: { colorScheme: 'orange' } },
          { name: '<PERSON>rror', props: { colorScheme: 'red' } }
        ]
      },
      {
        id: 'typography',
        name: 'Typography',
        category: 'foundations',
        description: 'Font families, sizes, weights, and text styles',
        variants: [
          { name: 'Headings', props: { type: 'headings' } },
          { name: 'Body Text', props: { type: 'body' } },
          { name: 'Captions', props: { type: 'captions' } }
        ]
      },
      {
        id: 'spacing',
        name: 'Spacing',
        category: 'foundations',
        description: 'Spacing scale and layout tokens',
        variants: [
          { name: 'Scale', props: { type: 'scale' } },
          { name: 'Layout', props: { type: 'layout' } }
        ]
      },
      {
        id: 'shadows',
        name: 'Shadows',
        category: 'foundations',
        description: 'Shadow tokens for depth and elevation',
        variants: [
          { name: 'Elevation', props: { type: 'elevation' } },
          { name: 'Focus', props: { type: 'focus' } }
        ]
      }
    ]
  },
  {
    id: 'shared-components',
    title: 'Shared Components',
    description: 'Reusable UI components used across applications',
    components: [
      {
        id: 'button',
        name: 'Button',
        category: 'shared-components',
        description: 'Interactive button component with multiple variants',
        variants: [
          { name: 'Primary', props: { variant: 'solid', colorScheme: 'blue' } },
          { name: 'Secondary', props: { variant: 'outline', colorScheme: 'blue' } },
          { name: 'Ghost', props: { variant: 'ghost', colorScheme: 'blue' } },
          { name: 'Link', props: { variant: 'plain', colorScheme: 'blue' } }
        ]
      },
      {
        id: 'input',
        name: 'Input',
        category: 'shared-components',
        description: 'Text input component with validation states',
        variants: [
          { name: 'Default', props: { placeholder: 'Enter text...' } },
          { name: 'Error', props: { invalid: true, placeholder: 'Invalid input' } },
          { name: 'Disabled', props: { disabled: true, placeholder: 'Disabled input' } }
        ]
      },
      {
        id: 'card',
        name: 'Card',
        category: 'shared-components',
        description: 'Container component for grouping related content',
        variants: [
          { name: 'Default', props: {} },
          { name: 'Elevated', props: { variant: 'elevated' } },
          { name: 'Outlined', props: { variant: 'outline' } }
        ]
      }
    ]
  },
  {
    id: 'shared-assets',
    title: 'Shared Assets',
    description: 'Icons, illustrations, and other visual assets',
    components: [
      {
        id: 'icons',
        name: 'Icons',
        category: 'shared-assets',
        description: 'Icon library and usage guidelines',
        variants: [
          { name: 'Interface', props: { category: 'interface' } },
          { name: 'Actions', props: { category: 'actions' } },
          { name: 'Status', props: { category: 'status' } }
        ]
      },
      {
        id: 'illustrations',
        name: 'Illustrations',
        category: 'shared-assets',
        description: 'Illustration library for empty states and onboarding',
        variants: [
          { name: 'Empty States', props: { type: 'empty' } },
          { name: 'Onboarding', props: { type: 'onboarding' } },
          { name: 'Error States', props: { type: 'error' } }
        ]
      }
    ]
  },
  {
    id: 'marketing-website-examples',
    title: 'Marketing Website Examples',
    description: 'Complete page examples for marketing websites',
    components: [
      {
        id: 'landing-page',
        name: 'Landing Page',
        category: 'marketing-website-examples',
        description: 'Complete landing page layout with hero, features, and CTA',
        variants: [
          { name: 'SaaS', props: { type: 'saas' } },
          { name: 'Product', props: { type: 'product' } },
          { name: 'Agency', props: { type: 'agency' } }
        ]
      },
      {
        id: 'pricing-page',
        name: 'Pricing Page',
        category: 'marketing-website-examples',
        description: 'Pricing page with plans and feature comparison',
        variants: [
          { name: 'Simple', props: { layout: 'simple' } },
          { name: 'Comparison', props: { layout: 'comparison' } }
        ]
      }
    ]
  },
  {
    id: 'marketing-website-components',
    title: 'Marketing Website Components',
    description: 'Specialized components for marketing websites',
    components: [
      {
        id: 'hero-section',
        name: 'Hero Section',
        category: 'marketing-website-components',
        description: 'Hero section with headline, description, and CTA',
        variants: [
          { name: 'Centered', props: { alignment: 'center' } },
          { name: 'Left Aligned', props: { alignment: 'left' } },
          { name: 'With Image', props: { hasImage: true } }
        ]
      },
      {
        id: 'feature-grid',
        name: 'Feature Grid',
        category: 'marketing-website-components',
        description: 'Grid layout for showcasing product features',
        variants: [
          { name: '3 Column', props: { columns: 3 } },
          { name: '4 Column', props: { columns: 4 } },
          { name: 'With Icons', props: { hasIcons: true } }
        ]
      }
    ]
  },
  {
    id: 'application-examples',
    title: 'Application Examples',
    description: 'Complete application page layouts and flows',
    components: [
      {
        id: 'dashboard',
        name: 'Dashboard',
        category: 'application-examples',
        description: 'Complete dashboard layout with sidebar and content area',
        variants: [
          { name: 'Analytics', props: { type: 'analytics' } },
          { name: 'CRM', props: { type: 'crm' } },
          { name: 'E-commerce', props: { type: 'ecommerce' } }
        ]
      },
      {
        id: 'settings-page',
        name: 'Settings Page',
        category: 'application-examples',
        description: 'Settings page with tabs and form sections',
        variants: [
          { name: 'Profile', props: { section: 'profile' } },
          { name: 'Security', props: { section: 'security' } },
          { name: 'Billing', props: { section: 'billing' } }
        ]
      }
    ]
  },
  {
    id: 'application-components',
    title: 'Application Components',
    description: 'Complex components for application interfaces',
    components: [
      {
        id: 'data-table',
        name: 'Data Table',
        category: 'application-components',
        description: 'Advanced data table with sorting, filtering, and pagination',
        variants: [
          { name: 'Basic', props: { features: ['sorting'] } },
          { name: 'Advanced', props: { features: ['sorting', 'filtering', 'pagination'] } },
          { name: 'Selectable', props: { selectable: true } }
        ]
      },
      {
        id: 'sidebar-navigation',
        name: 'Sidebar Navigation',
        category: 'application-components',
        description: 'Collapsible sidebar navigation for applications',
        variants: [
          { name: 'Expanded', props: { collapsed: false } },
          { name: 'Collapsed', props: { collapsed: true } },
          { name: 'With Submenu', props: { hasSubmenu: true } }
        ]
      }
    ]
  }
];

// Utility functions
export function getCategoryById(id: DesignSystemCategory): CategoryConfig | undefined {
  return designSystemCategories.find(category => category.id === id);
}

export function getComponentById(componentId: string): DesignSystemComponent | undefined {
  for (const category of designSystemCategories) {
    const component = category.components.find(comp => comp.id === componentId);
    if (component) return component;
  }
  return undefined;
}

export function getAllComponents(): DesignSystemComponent[] {
  return designSystemCategories.flatMap(category => category.components);
}

export function getComponentsByCategory(categoryId: DesignSystemCategory): DesignSystemComponent[] {
  const category = getCategoryById(categoryId);
  return category?.components || [];
}
