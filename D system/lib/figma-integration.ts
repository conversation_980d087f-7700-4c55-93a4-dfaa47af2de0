import { FigmaNode, FigmaColor, DesignToken } from '@/types/design-system';

// Figma API Integration utilities
export class FigmaIntegration {
  private accessToken: string;
  private baseUrl = 'https://api.figma.com/v1';

  constructor(accessToken: string) {
    this.accessToken = accessToken;
  }

  // Fetch Figma file data
  async getFile(fileId: string) {
    const response = await fetch(`${this.baseUrl}/files/${fileId}`, {
      headers: {
        'X-Figma-Token': this.accessToken,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch Figma file: ${response.statusText}`);
    }

    return response.json();
  }

  // Fetch specific node from Figma
  async getNode(fileId: string, nodeId: string) {
    const response = await fetch(`${this.baseUrl}/files/${fileId}/nodes?ids=${nodeId}`, {
      headers: {
        'X-Figma-Token': this.accessToken,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch Figma node: ${response.statusText}`);
    }

    return response.json();
  }

  // Export node as image
  async exportImage(fileId: string, nodeId: string, format: 'png' | 'jpg' | 'svg' = 'png', scale = 2) {
    const response = await fetch(
      `${this.baseUrl}/images/${fileId}?ids=${nodeId}&format=${format}&scale=${scale}`,
      {
        headers: {
          'X-Figma-Token': this.accessToken,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to export Figma image: ${response.statusText}`);
    }

    return response.json();
  }
}

// Convert Figma color to CSS
export function figmaColorToCss(color: FigmaColor): string {
  const r = Math.round(color.r * 255);
  const g = Math.round(color.g * 255);
  const b = Math.round(color.b * 255);
  const a = color.a;

  if (a === 1) {
    return `rgb(${r}, ${g}, ${b})`;
  } else {
    return `rgba(${r}, ${g}, ${b}, ${a})`;
  }
}

// Convert Figma color to hex
export function figmaColorToHex(color: FigmaColor): string {
  const r = Math.round(color.r * 255);
  const g = Math.round(color.g * 255);
  const b = Math.round(color.b * 255);

  const toHex = (n: number) => n.toString(16).padStart(2, '0');
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}

// Extract design tokens from Figma node
export function extractDesignTokens(node: FigmaNode): DesignToken[] {
  const tokens: DesignToken[] = [];

  // Extract color tokens
  if (node.fills) {
    node.fills.forEach((fill, index) => {
      if (fill.type === 'SOLID' && fill.color) {
        tokens.push({
          name: `${node.name.toLowerCase().replace(/\s+/g, '-')}-color-${index}`,
          value: figmaColorToHex(fill.color),
          type: 'color',
          description: `Color from ${node.name}`,
          figmaId: node.id,
        });
      }
    });
  }

  // Extract size tokens
  if (node.absoluteBoundingBox) {
    tokens.push(
      {
        name: `${node.name.toLowerCase().replace(/\s+/g, '-')}-width`,
        value: node.absoluteBoundingBox.width,
        type: 'size',
        description: `Width of ${node.name}`,
        figmaId: node.id,
      },
      {
        name: `${node.name.toLowerCase().replace(/\s+/g, '-')}-height`,
        value: node.absoluteBoundingBox.height,
        type: 'size',
        description: `Height of ${node.name}`,
        figmaId: node.id,
      }
    );
  }

  // Extract shadow tokens
  if (node.effects) {
    node.effects.forEach((effect, index) => {
      if (effect.type === 'DROP_SHADOW' && effect.color) {
        const shadowValue = `${effect.offset?.x || 0}px ${effect.offset?.y || 0}px ${
          effect.radius || 0
        }px ${figmaColorToCss(effect.color)}`;

        tokens.push({
          name: `${node.name.toLowerCase().replace(/\s+/g, '-')}-shadow-${index}`,
          value: shadowValue,
          type: 'shadow',
          description: `Shadow from ${node.name}`,
          figmaId: node.id,
        });
      }
    });
  }

  return tokens;
}

// Generate CSS custom properties from design tokens
export function generateCssCustomProperties(tokens: DesignToken[]): string {
  const cssProperties = tokens.map(token => {
    const propertyName = `--${token.name}`;
    return `  ${propertyName}: ${token.value};`;
  });

  return `:root {\n${cssProperties.join('\n')}\n}`;
}

// Generate Chakra UI theme tokens from design tokens
export function generateChakraTokens(tokens: DesignToken[]): Record<string, any> {
  const chakraTokens: Record<string, any> = {
    colors: {},
    sizes: {},
    shadows: {},
    spacing: {},
  };

  tokens.forEach(token => {
    switch (token.type) {
      case 'color':
        chakraTokens.colors[token.name] = { value: token.value };
        break;
      case 'size':
        chakraTokens.sizes[token.name] = { value: `${token.value}px` };
        break;
      case 'shadow':
        chakraTokens.shadows[token.name] = { value: token.value };
        break;
      case 'spacing':
        chakraTokens.spacing[token.name] = { value: `${token.value}px` };
        break;
    }
  });

  return chakraTokens;
}

// Mock Figma integration for development (when no access token is available)
export class MockFigmaIntegration {
  async getFile(fileId: string) {
    return {
      document: {
        id: 'mock-document',
        name: 'Mock Design System',
        children: [
          {
            id: 'mock-page',
            name: 'Design System',
            children: [
              {
                id: 'mock-frame',
                name: 'Components',
                type: 'FRAME',
                children: [],
              },
            ],
          },
        ],
      },
    };
  }

  async getNode(fileId: string, nodeId: string) {
    return {
      nodes: {
        [nodeId]: {
          document: {
            id: nodeId,
            name: 'Mock Component',
            type: 'COMPONENT',
            fills: [
              {
                type: 'SOLID',
                color: { r: 0.2, g: 0.4, b: 0.8, a: 1 },
              },
            ],
            absoluteBoundingBox: {
              x: 0,
              y: 0,
              width: 200,
              height: 100,
            },
          },
        },
      },
    };
  }

  async exportImage(fileId: string, nodeId: string) {
    return {
      images: {
        [nodeId]: 'https://via.placeholder.com/400x200/3182ce/ffffff?text=Mock+Component',
      },
    };
  }
}

// Create Figma integration instance
export function createFigmaIntegration(accessToken?: string) {
  if (accessToken) {
    return new FigmaIntegration(accessToken);
  } else {
    return new MockFigmaIntegration();
  }
}

// Figma file specific utilities
export const FIGMA_FILE_ID = 'CYlewnUysOEtOe6BVlSlZ3';

// Parse Figma design system structure
export function parseFigmaDesignSystem(figmaData: any) {
  const designSystemStructure = {
    foundations: {
      colors: [],
      typography: [],
      spacing: [],
      shadows: [],
    },
    components: {
      buttons: [],
      inputs: [],
      cards: [],
      navigation: [],
    },
    patterns: {
      layouts: [],
      forms: [],
      dataDisplay: [],
    }
  };

  // This would parse the actual Figma structure
  // For now, return mock structure based on the URL structure
  return designSystemStructure;
}

// Extract design tokens from Figma frames
export function extractDesignTokensFromFigma(figmaNode: any) {
  const tokens: DesignToken[] = [];

  // Extract color tokens from fills
  if (figmaNode.fills) {
    figmaNode.fills.forEach((fill: any, index: number) => {
      if (fill.type === 'SOLID' && fill.color) {
        tokens.push({
          name: `${figmaNode.name.toLowerCase().replace(/\s+/g, '-')}-${index}`,
          value: figmaColorToHex(fill.color),
          type: 'color',
          description: `Color from ${figmaNode.name}`,
          figmaId: figmaNode.id,
        });
      }
    });
  }

  // Extract typography tokens
  if (figmaNode.style) {
    const style = figmaNode.style;
    if (style.fontSize) {
      tokens.push({
        name: `${figmaNode.name.toLowerCase().replace(/\s+/g, '-')}-font-size`,
        value: `${style.fontSize}px`,
        type: 'typography',
        description: `Font size from ${figmaNode.name}`,
        figmaId: figmaNode.id,
      });
    }
  }

  return tokens;
}

// Convert Figma component to RSC structure
export function figmaComponentToRSC(figmaComponent: any) {
  return {
    id: figmaComponent.name.toLowerCase().replace(/\s+/g, '-'),
    name: figmaComponent.name,
    description: figmaComponent.description || `${figmaComponent.name} component from Figma`,
    figmaId: figmaComponent.id,
    figmaUrl: `https://www.figma.com/design/${FIGMA_FILE_ID}?node-id=${figmaComponent.id}`,
    variants: figmaComponent.children?.map((child: any) => ({
      name: child.name,
      props: extractPropsFromFigmaVariant(child),
      figmaId: child.id,
    })) || [],
    tokens: extractDesignTokensFromFigma(figmaComponent),
  };
}

// Extract props from Figma variant
function extractPropsFromFigmaVariant(figmaVariant: any) {
  const props: Record<string, any> = {};

  // Extract common props from Figma properties
  if (figmaVariant.name.includes('Primary')) {
    props.variant = 'primary';
  } else if (figmaVariant.name.includes('Secondary')) {
    props.variant = 'secondary';
  } else if (figmaVariant.name.includes('Outline')) {
    props.variant = 'outline';
  }

  if (figmaVariant.name.includes('Large')) {
    props.size = 'lg';
  } else if (figmaVariant.name.includes('Small')) {
    props.size = 'sm';
  } else {
    props.size = 'md';
  }

  // Extract color scheme
  if (figmaVariant.name.includes('Blue')) {
    props.colorScheme = 'blue';
  } else if (figmaVariant.name.includes('Green')) {
    props.colorScheme = 'green';
  } else if (figmaVariant.name.includes('Red')) {
    props.colorScheme = 'red';
  }

  return props;
}
