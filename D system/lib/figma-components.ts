// Real Figma components extracted from the design system file
import { DesignSystemComponent, CategoryConfig } from '@/types/design-system';

// Load the synced components from Figma
export const figmaComponents: DesignSystemComponent[] = [
  {
    id: 'logomark',
    name: 'Logomark',
    category: 'foundations',
    description: 'Brand logomark component from Figma design system',
    figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1083:50505',
    figmaId: '1083:50505',
    variants: [
      { name: 'Default', props: {} },
      { name: 'Small', props: { size: 'sm' } },
      { name: 'Large', props: { size: 'lg' } }
    ]
  },
  {
    id: 'swatch-base',
    name: 'Color Swatch',
    category: 'foundations',
    description: 'Color swatch component for displaying color tokens',
    figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1029:37647',
    figmaId: '1029:37647',
    variants: [
      { name: 'Solid', props: { type: 'solid' } },
      { name: 'Gradient', props: { type: 'gradient' } }
    ]
  },
  {
    id: 'type-scale-base',
    name: 'Typography Scale',
    category: 'foundations',
    description: 'Typography scale component showing font sizes and weights',
    figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1019:35537',
    figmaId: '1019:35537',
    variants: [
      { name: 'Display', props: { category: 'display' } },
      { name: 'Headings', props: { category: 'headings' } },
      { name: 'Body', props: { category: 'body' } },
      { name: 'Small', props: { category: 'small' } }
    ]
  },
  {
    id: 'button-icon',
    name: 'Button with Icon',
    category: 'shared-components',
    description: 'Button component with icon variants from Figma',
    figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1046:10170',
    figmaId: '1046:10170',
    variants: [
      { name: 'No Icon', props: { icon: false } },
      { name: 'Leading Icon', props: { icon: 'leading' } },
      { name: 'Trailing Icon', props: { icon: 'trailing' } }
    ]
  },
  {
    id: 'size-variants',
    name: 'Size Variants',
    category: 'shared-components',
    description: 'Component size variants (sm, md, lg)',
    figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=2680:401975',
    figmaId: '2680:401975',
    variants: [
      { name: 'Small', props: { size: 'sm' } },
      { name: 'Medium', props: { size: 'md' } },
      { name: 'Large', props: { size: 'lg' } }
    ]
  },
  {
    id: 'design-system-footer',
    name: 'Design System Footer',
    category: 'shared-components',
    description: 'Footer component for design system documentation',
    figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1023:37095',
    figmaId: '1023:37095',
    variants: [
      { name: 'Default', props: {} },
      { name: 'Compact', props: { variant: 'compact' } }
    ]
  },
  {
    id: 'safari-mockup',
    name: 'Safari Browser Mockup',
    category: 'shared-assets',
    description: 'Safari browser mockup for showcasing web applications',
    figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1868:671028',
    figmaId: '1868:671028',
    variants: [
      { name: 'With Address Bar', props: { addressBar: true } },
      { name: 'Clean', props: { addressBar: false } }
    ]
  },
  {
    id: 'screen-mockup',
    name: 'Screen Mockup',
    category: 'shared-assets',
    description: 'Device screen mockup for presentations',
    figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1316:3497',
    figmaId: '1316:3497',
    variants: [
      { name: 'Desktop', props: { breakpoint: 'desktop' } },
      { name: 'Tablet', props: { breakpoint: 'tablet' } },
      { name: 'Mobile', props: { breakpoint: 'mobile' } }
    ]
  },
  {
    id: 'email-template',
    name: 'Email Template',
    category: 'shared-assets',
    description: 'Email template for user invitations and notifications',
    figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=4057:415518',
    figmaId: '4057:415518',
    variants: [
      { name: 'User Invite', props: { type: 'user-invite' } },
      { name: 'Welcome', props: { type: 'welcome' } },
      { name: 'Notification', props: { type: 'notification' } }
    ]
  },
  {
    id: 'footer-layout',
    name: 'Footer Layout',
    category: 'marketing-website-components',
    description: 'Website footer with multiple column layouts',
    figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1282:12816',
    figmaId: '1282:12816',
    variants: [
      { name: '4 Column', props: { columns: 4 } },
      { name: '3 Column', props: { columns: 3 } },
      { name: '2 Column', props: { columns: 2 } }
    ]
  },
  {
    id: 'maps-integration',
    name: 'Maps Integration',
    category: 'application-components',
    description: 'Google Maps and vector map components',
    figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1438:225984',
    figmaId: '1438:225984',
    variants: [
      { name: 'Google Maps', props: { type: 'google-maps' } },
      { name: 'Vector Map', props: { type: 'vector' } }
    ]
  },
  {
    id: 'command-bar',
    name: 'Command Bar',
    category: 'application-components',
    description: 'Command palette for quick actions and navigation',
    figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=4898:411379',
    figmaId: '4898:411379',
    variants: [
      { name: 'Default', props: {} },
      { name: 'With Footer', props: { footer: true } }
    ]
  },
  {
    id: 'charts',
    name: 'Chart Components',
    category: 'application-components',
    description: 'Data visualization charts including radar charts',
    figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1084:7152',
    figmaId: '1084:7152',
    variants: [
      { name: 'Radar Chart', props: { type: 'radar' } },
      { name: 'Bar Chart', props: { type: 'bar' } },
      { name: 'Line Chart', props: { type: 'line' } }
    ]
  },
  {
    id: 'notifications',
    name: 'Notification System',
    category: 'application-components',
    description: 'Notification components for desktop applications',
    figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1251:2998',
    figmaId: '1251:2998',
    variants: [
      { name: 'Toast', props: { type: 'toast' } },
      { name: 'Banner', props: { type: 'banner' } },
      { name: 'Modal', props: { type: 'modal' } }
    ]
  },
  {
    id: 'messaging',
    name: 'Messaging Interface',
    category: 'application-components',
    description: 'Chat and messaging components for applications',
    figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=1247:167',
    figmaId: '1247:167',
    variants: [
      { name: 'Chat Bubble', props: { type: 'bubble' } },
      { name: 'Message List', props: { type: 'list' } },
      { name: 'Input Area', props: { type: 'input' } }
    ]
  }
];

// Enhanced categories with real Figma components
export const figmaEnhancedCategories: CategoryConfig[] = [
  {
    id: 'thumbnail',
    title: 'Thumbnail',
    description: 'Overview and preview of the design system',
    components: [
      {
        id: 'overview',
        name: 'Design System Overview',
        category: 'thumbnail',
        description: 'Complete overview of the design system components and guidelines',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3/Design-System?node-id=2210-441096',
        variants: [{ name: 'Default', props: {} }]
      }
    ]
  },
  {
    id: 'foundations',
    title: 'Foundations',
    description: 'Core design tokens, colors, typography, and spacing from Figma',
    components: figmaComponents.filter(c => c.category === 'foundations')
  },
  {
    id: 'shared-components',
    title: 'Shared Components',
    description: 'Reusable UI components synced from Figma design system',
    components: figmaComponents.filter(c => c.category === 'shared-components')
  },
  {
    id: 'shared-assets',
    title: 'Shared Assets',
    description: 'Icons, mockups, and visual assets from Figma',
    components: figmaComponents.filter(c => c.category === 'shared-assets')
  },
  {
    id: 'marketing-website-examples',
    title: 'Marketing Website Examples',
    description: 'Complete page examples for marketing websites',
    components: [
      {
        id: 'landing-pages',
        name: 'Landing Pages',
        category: 'marketing-website-examples',
        description: 'Complete landing page layouts from Figma',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=landing-pages',
        variants: [
          { name: 'SaaS Landing', props: { type: 'saas' } },
          { name: 'Product Landing', props: { type: 'product' } },
          { name: 'Agency Landing', props: { type: 'agency' } }
        ]
      },
      {
        id: 'pricing-pages',
        name: 'Pricing Pages',
        category: 'marketing-website-examples',
        description: 'Pricing page layouts with different structures',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=pricing-pages',
        variants: [
          { name: 'Simple Pricing', props: { layout: 'simple' } },
          { name: 'Feature Comparison', props: { layout: 'comparison' } }
        ]
      }
    ]
  },
  {
    id: 'marketing-website-components',
    title: 'Marketing Website Components',
    description: 'Marketing-specific components from Figma',
    components: figmaComponents.filter(c => c.category === 'marketing-website-components')
  },
  {
    id: 'application-examples',
    title: 'Application Examples',
    description: 'Complete application layouts from Figma',
    components: [
      {
        id: 'dashboards',
        name: 'Dashboard Examples',
        category: 'application-examples',
        description: 'Complete dashboard layouts from Figma',
        figmaUrl: 'https://www.figma.com/design/CYlewnUysOEtOe6BVlSlZ3?node-id=dashboards',
        variants: [
          { name: 'Analytics Dashboard', props: { type: 'analytics' } },
          { name: 'CRM Dashboard', props: { type: 'crm' } },
          { name: 'E-commerce Dashboard', props: { type: 'ecommerce' } }
        ]
      }
    ]
  },
  {
    id: 'application-components',
    title: 'Application Components',
    description: 'Complex application components synced from Figma',
    components: figmaComponents.filter(c => c.category === 'application-components')
  }
];

// Function to get Figma component by ID
export function getFigmaComponentById(id: string): DesignSystemComponent | undefined {
  return figmaComponents.find(component => component.id === id);
}

// Function to get all Figma components by category
export function getFigmaComponentsByCategory(category: string): DesignSystemComponent[] {
  return figmaComponents.filter(component => component.category === category);
}
