import { createFigmaIntegration, FIGMA_FILE_ID, figmaComponentToRSC } from './figma-integration';
import { DesignSystemComponent, DesignToken } from '@/types/design-system';

// Figma sync utilities for the specific design system file
export class FigmaDesignSystemSync {
  private figmaIntegration: any;
  private fileId: string;

  constructor(accessToken?: string) {
    this.figmaIntegration = createFigmaIntegration(accessToken);
    this.fileId = FIGMA_FILE_ID;
  }

  // Sync all components from Figma
  async syncAllComponents(): Promise<DesignSystemComponent[]> {
    try {
      const fileData = await this.figmaIntegration.getFile(this.fileId);
      const components: DesignSystemComponent[] = [];

      // Parse the Figma file structure
      if (fileData.document && fileData.document.children) {
        for (const page of fileData.document.children) {
          if (page.name.toLowerCase().includes('components') || 
              page.name.toLowerCase().includes('design system')) {
            const pageComponents = await this.parsePageComponents(page);
            components.push(...pageComponents);
          }
        }
      }

      return components;
    } catch (error) {
      console.error('Error syncing components from Figma:', error);
      return [];
    }
  }

  // Parse components from a Figma page
  private async parsePageComponents(page: any): Promise<DesignSystemComponent[]> {
    const components: DesignSystemComponent[] = [];

    if (page.children) {
      for (const frame of page.children) {
        if (frame.type === 'FRAME' || frame.type === 'COMPONENT_SET') {
          const component = figmaComponentToRSC(frame);
          
          // Categorize component based on frame name or position
          component.category = this.categorizeComponent(frame.name);
          
          components.push(component);
        }
      }
    }

    return components;
  }

  // Categorize component based on its name or properties
  private categorizeComponent(componentName: string): any {
    const name = componentName.toLowerCase();
    
    if (name.includes('color') || name.includes('typography') || 
        name.includes('spacing') || name.includes('shadow')) {
      return 'foundations';
    } else if (name.includes('button') || name.includes('input') || 
               name.includes('card') || name.includes('badge')) {
      return 'shared-components';
    } else if (name.includes('icon') || name.includes('illustration')) {
      return 'shared-assets';
    } else if (name.includes('marketing') || name.includes('landing')) {
      return 'marketing-website-components';
    } else if (name.includes('dashboard') || name.includes('table') || 
               name.includes('navigation')) {
      return 'application-components';
    }
    
    return 'shared-components'; // default
  }

  // Sync design tokens from Figma
  async syncDesignTokens(): Promise<DesignToken[]> {
    try {
      const fileData = await this.figmaIntegration.getFile(this.fileId);
      const tokens: DesignToken[] = [];

      // Look for design tokens in styles
      if (fileData.styles) {
        for (const [styleId, style] of Object.entries(fileData.styles)) {
          const token = this.convertStyleToToken(style as any, styleId);
          if (token) {
            tokens.push(token);
          }
        }
      }

      return tokens;
    } catch (error) {
      console.error('Error syncing design tokens from Figma:', error);
      return [];
    }
  }

  // Convert Figma style to design token
  private convertStyleToToken(style: any, styleId: string): DesignToken | null {
    if (!style.name) return null;

    const tokenName = style.name.toLowerCase().replace(/\s+/g, '-').replace(/\//g, '-');
    
    // Determine token type based on style type
    let tokenType: DesignToken['type'] = 'color';
    let tokenValue: string | number = '';

    switch (style.styleType) {
      case 'FILL':
        tokenType = 'color';
        // Extract color value from style
        tokenValue = '#000000'; // placeholder
        break;
      case 'TEXT':
        tokenType = 'typography';
        tokenValue = `${style.fontSize || 16}px`;
        break;
      case 'EFFECT':
        tokenType = 'shadow';
        tokenValue = '0 2px 4px rgba(0,0,0,0.1)'; // placeholder
        break;
      default:
        return null;
    }

    return {
      name: tokenName,
      value: tokenValue,
      type: tokenType,
      description: style.description || `${style.name} from Figma`,
      figmaId: styleId,
    };
  }

  // Export component as image from Figma
  async exportComponentImage(componentId: string, format: 'png' | 'svg' = 'png'): Promise<string | null> {
    try {
      const result = await this.figmaIntegration.exportImage(this.fileId, componentId, format);
      return result.images?.[componentId] || null;
    } catch (error) {
      console.error('Error exporting component image:', error);
      return null;
    }
  }

  // Get component details from Figma
  async getComponentDetails(componentId: string): Promise<any> {
    try {
      const result = await this.figmaIntegration.getNode(this.fileId, componentId);
      return result.nodes?.[componentId]?.document || null;
    } catch (error) {
      console.error('Error getting component details:', error);
      return null;
    }
  }

  // Generate CSS from design tokens
  generateCSSFromTokens(tokens: DesignToken[]): string {
    const cssVariables = tokens.map(token => {
      return `  --${token.name}: ${token.value};`;
    }).join('\n');

    return `:root {\n${cssVariables}\n}`;
  }

  // Generate Chakra UI theme from design tokens
  generateChakraTheme(tokens: DesignToken[]): Record<string, any> {
    const theme: Record<string, any> = {
      colors: {},
      fontSizes: {},
      space: {},
      shadows: {},
    };

    tokens.forEach(token => {
      switch (token.type) {
        case 'color':
          theme.colors[token.name] = { value: token.value };
          break;
        case 'typography':
          theme.fontSizes[token.name] = { value: token.value };
          break;
        case 'spacing':
          theme.space[token.name] = { value: token.value };
          break;
        case 'shadow':
          theme.shadows[token.name] = { value: token.value };
          break;
      }
    });

    return theme;
  }
}

// Utility function to create a sync instance
export function createFigmaSync(accessToken?: string) {
  return new FigmaDesignSystemSync(accessToken);
}

// Mock data for development when Figma token is not available
export const mockFigmaComponents: DesignSystemComponent[] = [
  {
    id: 'figma-button',
    name: 'Figma Button',
    category: 'shared-components',
    description: 'Button component synced from Figma design system',
    figmaUrl: `https://www.figma.com/design/${FIGMA_FILE_ID}?node-id=button-component`,
    variants: [
      { name: 'Primary', props: { variant: 'primary', size: 'md' } },
      { name: 'Secondary', props: { variant: 'secondary', size: 'md' } },
      { name: 'Outline', props: { variant: 'outline', size: 'md' } },
    ],
    tokens: [
      { name: 'button-primary-bg', value: '#3b82f6', type: 'color', description: 'Primary button background' },
      { name: 'button-padding-x', value: '16px', type: 'spacing', description: 'Button horizontal padding' },
      { name: 'button-border-radius', value: '6px', type: 'border', description: 'Button border radius' },
    ]
  }
];

export const mockFigmaTokens: DesignToken[] = [
  { name: 'primary-50', value: '#eff6ff', type: 'color', description: 'Primary color lightest shade' },
  { name: 'primary-500', value: '#3b82f6', type: 'color', description: 'Primary color base' },
  { name: 'primary-900', value: '#1e3a8a', type: 'color', description: 'Primary color darkest shade' },
  { name: 'font-size-sm', value: '14px', type: 'typography', description: 'Small font size' },
  { name: 'font-size-base', value: '16px', type: 'typography', description: 'Base font size' },
  { name: 'font-size-lg', value: '18px', type: 'typography', description: 'Large font size' },
  { name: 'space-2', value: '8px', type: 'spacing', description: 'Small spacing' },
  { name: 'space-4', value: '16px', type: 'spacing', description: 'Base spacing' },
  { name: 'space-6', value: '24px', type: 'spacing', description: 'Large spacing' },
];
